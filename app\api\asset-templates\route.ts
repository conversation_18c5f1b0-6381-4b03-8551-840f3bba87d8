import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { sanitizeSVG, validateSVGContent, isAdminTemplate } from 'src/lib/svg-sanitizer';
import { deleteFile } from 'src/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const ownerOnly = searchParams.get('owner') === 'true';

    const supabase = getSupabaseAdminClient();

    let query = supabase
      .from('asset_templates')
      .select('*')
      .order('created_at', { ascending: false });

    if (ownerOnly) {
      // Filter to only templates created by this user
      query = query.eq('profile_email', session.user.email);
    }

    const { data: templates, error } = await query;

    if (error) {
      console.error('Error fetching asset templates:', error);
      return NextResponse.json({ error: 'Failed to fetch asset templates' }, { status: 500 });
    }

    return NextResponse.json({ templates: templates || [] });

  } catch (error) {
    console.error('Get asset templates error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const templateData = await request.json();

    // Validate required fields
    if (!templateData.id || !templateData.name || !templateData.asset_type || !templateData.svg_code) {
      return NextResponse.json({
        error: 'Missing required fields: id, name, asset_type, svg_code'
      }, { status: 400 });
    }

    // Validate SVG content
    const svgValidationErrors = validateSVGContent(templateData.svg_code);
    if (svgValidationErrors.length > 0) {
      return NextResponse.json({
        error: svgValidationErrors.join(', ')
      }, { status: 400 });
    }

    // Sanitize SVG for owner templates (admin templates are not sanitized)
    const isAdmin = isAdminTemplate(session.user.email === process.env.ADMIN_EMAIL ? null : session.user.email);
    try {
      templateData.svg_code = sanitizeSVG(templateData.svg_code, isAdmin);
    } catch (error) {
      return NextResponse.json({
        error: `SVG sanitization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }, { status: 400 });
    }

    // Set profile_email to current user for owner-created templates
    templateData.profile_email = session.user.email;

    const supabase = getSupabaseAdminClient();

    // Check if template ID already exists
    const { data: existing, error: checkError } = await supabase
      .from('asset_templates')
      .select('id')
      .eq('id', templateData.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing template:', checkError);
      return NextResponse.json({ error: 'Failed to check existing template' }, { status: 500 });
    }

    if (existing) {
      return NextResponse.json({ error: 'Template ID already exists' }, { status: 400 });
    }

    // Insert template
    const { data: template, error: insertError } = await supabase
      .from('asset_templates')
      .insert(templateData)
      .select()
      .single();

    if (insertError) {
      console.error('Template creation error:', insertError);
      return NextResponse.json({ error: 'Failed to create template' }, { status: 500 });
    }

    return NextResponse.json({ success: true, template });

  } catch (error) {
    console.error('Create asset template error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id, ...updateData } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user owns the template (only owner-created templates can be edited by owners)
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('asset_templates')
      .select('*')
      .eq('id', id)
      .eq('profile_email', session.user.email)
      .single();

    if (fetchError || !existingTemplate) {
      return NextResponse.json({ error: 'Template not found or access denied' }, { status: 404 });
    }

    // Remove fields that shouldn't be updated
    delete updateData.id;
    delete updateData.profile_email;
    delete updateData.created_at;

    // Validate and sanitize SVG if svg_code is being updated
    if (updateData.svg_code) {
      const svgValidationErrors = validateSVGContent(updateData.svg_code);
      if (svgValidationErrors.length > 0) {
        return NextResponse.json({
          error: svgValidationErrors.join(', ')
        }, { status: 400 });
      }

      // Sanitize SVG for owner templates (admin templates are not sanitized)
      const isAdmin = isAdminTemplate(existingTemplate.profile_email);
      try {
        updateData.svg_code = sanitizeSVG(updateData.svg_code, isAdmin);
      } catch (error) {
        return NextResponse.json({
          error: `SVG sanitization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        }, { status: 400 });
      }
    }

    // Update template
    const { data: updatedTemplate, error: updateError } = await supabase
      .from('asset_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Template update error:', updateError);
      return NextResponse.json({ error: 'Failed to update template' }, { status: 500 });
    }

    return NextResponse.json({ success: true, template: updatedTemplate });

  } catch (error) {
    console.error('Update asset template error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user owns the template (only owner-created templates can be deleted by owners)
    const { data: existingTemplate, error: fetchError } = await supabase
      .from('asset_templates')
      .select('*')
      .eq('id', id)
      .eq('profile_email', session.user.email)
      .single();

    if (fetchError || !existingTemplate) {
      return NextResponse.json({ error: 'Template not found or access denied' }, { status: 404 });
    }

    // Find all assets that use this template
    const { data: assetsUsingTemplate, error: assetsError } = await supabase
      .from('assets')
      .select('id, image_url')
      .eq('template_id', id)
      .eq('is_deleted', false);

    if (assetsError) {
      console.error('Error fetching assets using template:', assetsError);
      return NextResponse.json({ error: 'Failed to check template usage' }, { status: 500 });
    }

    // First, remove the template reference from all assets that use this template
    // This prevents foreign key constraint violations
    if (assetsUsingTemplate && assetsUsingTemplate.length > 0) {
      // Update all assets to remove template_id reference
      const { error: updateTemplateRefError } = await supabase
        .from('assets')
        .update({
          template_id: null,
          updated_at: new Date().toISOString()
        })
        .eq('template_id', id)
        .eq('is_deleted', false);

      if (updateTemplateRefError) {
        console.error('Error removing template references from assets:', updateTemplateRefError);
        return NextResponse.json({ error: 'Failed to remove template references from assets' }, { status: 500 });
      }

      // Now delete all assets and their references that used this template
      for (const asset of assetsUsingTemplate) {
        // Delete asset image from storage
        if (asset.image_url) {
          try {
            const urlParts = asset.image_url.split('/');
            const fileName = urlParts[urlParts.length - 1];
            const filePath = `assets/${fileName}`;
            await deleteFile('images', filePath);
          } catch (error) {
            console.error('Error deleting asset image:', error);
            // Continue with deletion even if image deletion fails
          }
        }

        // Delete all asset transfers for this asset
        const { error: transferDeleteError } = await supabase
          .from('asset_transfers')
          .delete()
          .eq('asset_id', asset.id);

        if (transferDeleteError) {
          console.error('Error deleting asset transfers:', transferDeleteError);
          // Continue with deletion even if transfer deletion fails
        }

        // Soft delete the asset
        const { error: assetDeleteError } = await supabase
          .from('assets')
          .update({
            is_deleted: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', asset.id);

        if (assetDeleteError) {
          console.error('Error deleting asset:', assetDeleteError);
          // Continue with deletion even if asset deletion fails
        }
      }
    }

    // Delete the template
    const { error: deleteError } = await supabase
      .from('asset_templates')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Template deletion error:', deleteError);
      return NextResponse.json({ error: 'Failed to delete template' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `Template and ${assetsUsingTemplate?.length || 0} associated assets deleted successfully`
    });

  } catch (error) {
    console.error('Delete asset template error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
