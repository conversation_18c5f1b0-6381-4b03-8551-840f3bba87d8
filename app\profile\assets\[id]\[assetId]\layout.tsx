// app/profile/assets/[id]/[assetId]/layout.tsx
// Dynamic metadata for individual asset pages

import type { Metadata } from 'next';
import { ReactNode } from 'react';
import { getSupabaseClient } from 'src/lib/supabase';
import { VIEW_PROFILE_URL, DEFAULT_AVATAR_URL } from 'src/lib/config';

export async function generateMetadata(
  { params }: { params: Promise<{ id: string; assetId: string }> }
): Promise<Metadata> {
  const { id, assetId } = await params;
  const contactName = decodeURIComponent(id).toLowerCase();

  // Default fallback values
  const defaultTitle = `Asset - ${contactName} | ODude`;
  const defaultDescription = `View ${contactName}'s digital asset on ODude.`;
  const defaultImage = DEFAULT_AVATAR_URL;
  const assetUrl = `${VIEW_PROFILE_URL}${contactName}/assets/${assetId}`;

  try {
    const client = getSupabaseClient();
    
    // Fetch asset with transfer information
    const { data: transferData } = await client
      .from('asset_transfers')
      .select(`
        *,
        assets (
          id,
          title,
          description,
          asset_type,
          image_url,
          issuer_odude_name,
          created_at
        )
      `)
      .eq('asset_id', assetId)
      .eq('to_odude_name', contactName)
      .single();

    // Fetch recipient profile info
    const { data: recipientData } = await client
      .from('contact')
      .select('profile, name, description, images')
      .eq('name', contactName)
      .single();

    // Fetch issuer profile info if asset exists
    let issuerData = null;
    if (transferData?.assets?.issuer_odude_name) {
      const { data } = await client
        .from('contact')
        .select('profile, name, images')
        .eq('name', transferData.assets.issuer_odude_name)
        .single();
      issuerData = data;
    }

    if (transferData?.assets && recipientData) {
      const asset = transferData.assets;
      const title = `${asset.title} - ${recipientData.profile || contactName} | ODude`;
      const description = asset.description || 
        `${asset.asset_type} issued by ${asset.issuer_odude_name} to ${recipientData.profile || contactName} on ODude.`;
      
      // Use asset image or recipient's profile image
      const image = asset.image_url || 
        (recipientData.images ? 
          (typeof recipientData.images === 'string' ? 
            JSON.parse(recipientData.images).img1 : 
            recipientData.images.img1) || DEFAULT_AVATAR_URL : 
          DEFAULT_AVATAR_URL);

      return {
        title,
        description,
        // Open Graph tags
        openGraph: {
          title,
          description,
          url: assetUrl,
          siteName: 'ODude',
          images: [
            {
              url: image,
              width: 500,
              height: 400,
              alt: `${asset.title} - ${asset.asset_type}`,
            },
          ],
          locale: 'en_US',
          type: 'article',
        },
        // Twitter Card tags
        twitter: {
          card: 'summary_large_image',
          title,
          description,
          images: [image],
          creator: '@ODude',
          site: '@ODude',
        },
        // Additional meta tags
        other: {
          'asset:type': asset.asset_type,
          'asset:issuer': asset.issuer_odude_name,
          'asset:recipient': contactName,
          'asset:issued_date': asset.created_at,
          'article:author': issuerData?.profile || asset.issuer_odude_name,
          'article:published_time': asset.created_at,
        },
        // Canonical URL
        alternates: {
          canonical: assetUrl,
        },
      };
    }

    // Fallback if asset not found or not accessible
    return {
      title: defaultTitle,
      description: defaultDescription,
      // Open Graph tags (fallback)
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: assetUrl,
        siteName: 'ODude',
        images: [
          {
            url: defaultImage,
            width: 500,
            height: 400,
            alt: `${contactName}'s asset`,
          },
        ],
        locale: 'en_US',
        type: 'article',
      },
      // Twitter Card tags (fallback)
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
        images: [defaultImage],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags (fallback)
      other: {
        'asset:recipient': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: assetUrl,
      },
    };
  } catch (error) {
    console.error('Error generating asset metadata:', error);
    return {
      title: defaultTitle,
      description: defaultDescription,
      // Open Graph tags (fallback)
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: assetUrl,
        siteName: 'ODude',
        images: [
          {
            url: defaultImage,
            width: 500,
            height: 400,
            alt: `${contactName}'s asset`,
          },
        ],
        locale: 'en_US',
        type: 'article',
      },
      // Twitter Card tags (fallback)
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
        images: [defaultImage],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags (fallback)
      other: {
        'asset:recipient': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: assetUrl,
      },
    };
  }
}

export default function AssetLayout({
  children,
}: {
  children: ReactNode;
}) {
  return <>{children}</>;
}
