// app/profile/assets/[id]/layout.tsx
// Dynamic metadata for assets listing page

import type { Metadata } from 'next';
import { ReactNode } from 'react';
import { getSupabaseClient } from 'src/lib/supabase';
import { VIEW_PROFILE_URL, DEFAULT_AVATAR_URL } from 'src/lib/config';

export async function generateMetadata(
  { params }: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  const { id } = await params;
  const contactName = decodeURIComponent(id).toLowerCase();

  // Default fallback values
  const defaultTitle = `${contactName} Assets | ODude`;
  const defaultDescription = `View ${contactName}'s digital assets showcase on ODude.`;
  const defaultImage = DEFAULT_AVATAR_URL;
  const assetsUrl = `${VIEW_PROFILE_URL}${contactName}/assets`;

  try {
    const client = getSupabaseClient();
    
    // Fetch recipient profile info
    const { data: recipientData } = await client
      .from('contact')
      .select('profile, name, description, images')
      .eq('name', contactName)
      .single();

    if (recipientData) {
      const displayName = recipientData.profile || contactName;
      const title = `${contactName} Assets | ODude`;
      const description = recipientData.description || 
        `View ${displayName}'s digital assets showcase on ODude.`;
      
      // Use recipient's profile image
      const image = recipientData.images ? 
        (typeof recipientData.images === 'string' ? 
          JSON.parse(recipientData.images).img1 : 
          recipientData.images.img1) || DEFAULT_AVATAR_URL : 
        DEFAULT_AVATAR_URL;

      return {
        title,
        description,
        // Open Graph tags
        openGraph: {
          title,
          description,
          url: assetsUrl,
          siteName: 'ODude',
          images: [
            {
              url: image,
              width: 500,
              height: 400,
              alt: `${displayName}'s Assets`,
            },
          ],
          locale: 'en_US',
          type: 'profile',
        },
        // Twitter Card tags
        twitter: {
          card: 'summary_large_image',
          title,
          description,
          images: [image],
          creator: '@ODude',
          site: '@ODude',
        },
        // Additional meta tags
        other: {
          'profile:username': contactName,
          'profile:name': displayName,
        },
        // Canonical URL
        alternates: {
          canonical: assetsUrl,
        },
      };
    }

    // Fallback if profile not found
    return {
      title: defaultTitle,
      description: defaultDescription,
      // Open Graph tags (fallback)
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: assetsUrl,
        siteName: 'ODude',
        images: [
          {
            url: defaultImage,
            width: 500,
            height: 400,
            alt: `${contactName}'s assets`,
          },
        ],
        locale: 'en_US',
        type: 'profile',
      },
      // Twitter Card tags (fallback)
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
        images: [defaultImage],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags (fallback)
      other: {
        'profile:username': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: assetsUrl,
      },
    };
  } catch (error) {
    console.error('Error generating assets page metadata:', error);
    return {
      title: defaultTitle,
      description: defaultDescription,
      // Open Graph tags (fallback)
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: assetsUrl,
        siteName: 'ODude',
        images: [
          {
            url: defaultImage,
            width: 500,
            height: 400,
            alt: `${contactName}'s assets`,
          },
        ],
        locale: 'en_US',
        type: 'profile',
      },
      // Twitter Card tags (fallback)
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
        images: [defaultImage],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags (fallback)
      other: {
        'profile:username': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: assetsUrl,
      },
    };
  }
}

export default function AssetsLayout({
  children,
}: {
  children: ReactNode;
}) {
  return <>{children}</>;
}
