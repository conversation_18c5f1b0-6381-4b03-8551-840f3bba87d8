-- Migration script to add password authentication fields to profiles table
-- This enables username/password authentication alongside existing OAuth providers

-- Add password authentication fields to existing profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS password_hash TEXT,
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_token TEXT,
ADD COLUMN IF NOT EXISTS verification_token_expires TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS reset_token TEXT,
ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS auth_provider TEXT DEFAULT 'oauth' CHECK (auth_provider IN ('oauth', 'credentials', 'both'));

-- Add indexes for new fields to improve query performance
CREATE INDEX IF NOT EXISTS idx_profiles_email_verified ON profiles(email_verified);
CREATE INDEX IF NOT EXISTS idx_profiles_verification_token ON profiles(verification_token);
CREATE INDEX IF NOT EXISTS idx_profiles_reset_token ON profiles(reset_token);
CREATE INDEX IF NOT EXISTS idx_profiles_auth_provider ON profiles(auth_provider);

-- Add comments for documentation
COMMENT ON COLUMN profiles.password_hash IS 'Bcrypt hash of user password (NULL for OAuth-only users)';
COMMENT ON COLUMN profiles.email_verified IS 'Whether email address has been verified (TRUE for OAuth users)';
COMMENT ON COLUMN profiles.verification_token IS 'Token for email verification (expires after use or timeout)';
COMMENT ON COLUMN profiles.verification_token_expires IS 'Expiration timestamp for verification token';
COMMENT ON COLUMN profiles.reset_token IS 'Token for password reset (expires after use or timeout)';
COMMENT ON COLUMN profiles.reset_token_expires IS 'Expiration timestamp for reset token';
COMMENT ON COLUMN profiles.auth_provider IS 'Authentication method: oauth (Google/Facebook/GitHub), credentials (email/password), or both';

-- Update existing OAuth users to have email_verified = TRUE
-- Since OAuth providers verify emails during their authentication process
UPDATE profiles 
SET email_verified = TRUE, 
    auth_provider = 'oauth'
WHERE password_hash IS NULL;

-- Create a function to clean up expired tokens (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_expired_auth_tokens()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    UPDATE profiles 
    SET 
        verification_token = NULL,
        verification_token_expires = NULL
    WHERE verification_token_expires < NOW();
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    UPDATE profiles 
    SET 
        reset_token = NULL,
        reset_token_expires = NULL
    WHERE reset_token_expires < NOW();
    
    GET DIAGNOSTICS cleaned_count = cleaned_count + ROW_COUNT;
    
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;

-- Add comment for the cleanup function
COMMENT ON FUNCTION cleanup_expired_auth_tokens() IS 'Cleans up expired verification and reset tokens. Returns count of cleaned records.';

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Password authentication migration completed successfully';
    RAISE NOTICE 'New columns added: password_hash, email_verified, verification_token, verification_token_expires, reset_token, reset_token_expires, auth_provider';
    RAISE NOTICE 'Existing OAuth users updated with email_verified = TRUE and auth_provider = oauth';
    RAISE NOTICE 'Indexes created for optimal query performance';
    RAISE NOTICE 'Cleanup function created: cleanup_expired_auth_tokens()';
END $$;

-- Verification query to show the updated table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
    AND table_schema = 'public'
ORDER BY ordinal_position;
