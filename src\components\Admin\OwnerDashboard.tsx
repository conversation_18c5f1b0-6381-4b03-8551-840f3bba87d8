'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Tabs,
  Stack,
  Badge,
  Button,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconAddressBook,
  IconBookmark,
  IconShield,
  IconCoins,
  IconTemplate,
  IconPlus,
  IconHistory,
} from '@tabler/icons-react';
import { AdminStats, fetchAdminStats } from 'src/lib/admin';
import { ProfilesTable } from 'src/components/Admin/ProfilesTable';
import { ContactsTable } from 'src/components/Admin/ContactsTable';
import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { AdminAccessInfo } from 'src/lib/adminClient';
import { UserSettings, getUserSettings, hasSufficientPointsForContact, hasSufficientPointsForAssetTransfer } from 'src/lib/user-settings';
import { useSession } from 'next-auth/react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

interface OwnerDashboardProps {
  adminAccess: AdminAccessInfo;
}

export function OwnerDashboard({ adminAccess }: OwnerDashboardProps) {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userSettings, setUserSettings] = useState<UserSettings | null>(null);
  const { data: session } = useSession();

  useEffect(() => {
    // Fetch admin stats and user settings
    const fetchData = async () => {
      try {
        const statsData = await fetchAdminStats();
        setStats(statsData);

        // Fetch user settings if we have a session
        if (session?.user?.email) {
          const settings = await getUserSettings(session.user.email);
          setUserSettings(settings);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [session?.user?.email]);

  const handleCreateAssetClick = async () => {
    if (!session?.user?.email) return;

    const pointCheck = await hasSufficientPointsForAssetTransfer(session.user.email);

    if (!pointCheck.sufficient) {
      notifications.show({
        title: 'Insufficient Points',
        message: `You need ${pointCheck.requiredPoints} points to create assets. You currently have ${pointCheck.currentPoints} points.`,
        color: 'red',
      });
      return;
    }

    modals.openConfirmModal({
      title: 'Create Asset',
      centered: true,
      children: (
        <Text size="sm">
          Creating an asset will deduct {pointCheck.requiredPoints} points from your balance.
          You currently have {pointCheck.currentPoints} points. Do you want to proceed?
        </Text>
      ),
      labels: { confirm: 'Proceed', cancel: 'Cancel' },
      confirmProps: { color: 'blue' },
      onConfirm: () => {
        window.location.href = '/owner?tab=assets';
      },
    });
  };

  const handleCreateContactClick = async () => {
    if (!session?.user?.email) return;

    const pointCheck = await hasSufficientPointsForContact(session.user.email);

    if (!pointCheck.sufficient) {
      notifications.show({
        title: 'Insufficient Points',
        message: `You need ${pointCheck.requiredPoints} points to create contacts. You currently have ${pointCheck.currentPoints} points.`,
        color: 'red',
      });
      return;
    }

    modals.openConfirmModal({
      title: 'Create Contact',
      centered: true,
      children: (
        <Text size="sm">
          Creating a contact will deduct {pointCheck.requiredPoints} points from your balance.
          You currently have {pointCheck.currentPoints} points. Do you want to proceed?
        </Text>
      ),
      labels: { confirm: 'Proceed', cancel: 'Cancel' },
      confirmProps: { color: 'orange' },
      onConfirm: () => {
        window.location.href = '/tools';
      },
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconShield size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Owner Dashboard</Title>
              <Text c="dimmed">
                Manage your namespace: {adminAccess.ownedPrimaryNames.join(', ')}
              </Text>
              <Group gap="xs" mt="xs">
                {adminAccess.ownedPrimaryNames.map((name) => (
                  <Badge key={name} variant="light" color="blue">
                    {name}
                  </Badge>
                ))}
              </Group>
            </div>
          </Group>
          <Group>
            {/* Action Buttons based on user settings */}
            {userSettings?.asset_issuer && (
              <>
                <Button
                  variant="light"
                  leftSection={<IconPlus size={16} />}
                  onClick={handleCreateAssetClick}
                  color="blue"
                >
                  Create Asset
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconHistory size={16} />}
                  onClick={() => window.location.href = '/admin/points'}
                  color="green"
                >
                  View Transactions
                </Button>
              </>
            )}
            {userSettings?.create_template && (
              <Button
                variant="light"
                leftSection={<IconTemplate size={16} />}
                onClick={() => window.location.href = '/owner?tab=templates'}
                color="purple"
              >
                Create Template
              </Button>
            )}
            {userSettings?.create_contact && (
              <Button
                variant="light"
                leftSection={<IconAddressBook size={16} />}
                onClick={handleCreateContactClick}
                color="orange"
              >
                Create Contact
              </Button>
            )}
          </Group>
        </Group>

        {/* Statistics Cards */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
            <StatCard
              title="Profiles in Namespace"
              value={stats.profiles}
              icon={<IconUsers size={18} />}
              color="blue"
            />
            <StatCard
              title="Contacts in Namespace"
              value={stats.contacts}
              icon={<IconAddressBook size={18} />}
              color="green"
            />
            <StatCard
              title="Bookmarks in Namespace"
              value={stats.bookmarks}
              icon={<IconBookmark size={18} />}
              color="orange"
            />
          </SimpleGrid>
        )}

        {/* Data Tables */}
        <Tabs defaultValue="contacts" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="contacts" leftSection={<IconAddressBook size={16} />}>
              Contacts
            </Tabs.Tab>
            <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
              Profiles
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="contacts" pt="md">
            <ContactsTable />
          </Tabs.Panel>

          <Tabs.Panel value="profiles" pt="md">
            <ProfilesTable />
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </AdminLayout>
  );
}
