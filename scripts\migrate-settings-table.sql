-- Migration script to add new columns to settings table
-- This script adds asset_issuer, create_template, and create_contact columns

-- Add new columns to settings table if they don't exist
DO $$ 
BEGIN
    -- Add asset_issuer column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'settings' 
        AND column_name = 'asset_issuer'
    ) THEN
        ALTER TABLE settings ADD COLUMN asset_issuer BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added asset_issuer column to settings table';
    END IF;

    -- Add create_template column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'settings' 
        AND column_name = 'create_template'
    ) THEN
        ALTER TABLE settings ADD COLUMN create_template BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added create_template column to settings table';
    END IF;

    -- Add create_contact column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'settings' 
        AND column_name = 'create_contact'
    ) THEN
        ALTER TABLE settings ADD COLUMN create_contact BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added create_contact column to settings table';
    END IF;
END $$;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_settings_asset_issuer ON settings(asset_issuer);
CREATE INDEX IF NOT EXISTS idx_settings_create_template ON settings(create_template);
CREATE INDEX IF NOT EXISTS idx_settings_create_contact ON settings(create_contact);

-- Drop user_qr_services table if it exists (no longer needed)
DROP TABLE IF EXISTS user_qr_services CASCADE;

-- Update table comment
COMMENT ON TABLE settings IS 'User settings and permissions for various features';

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Settings table migration completed successfully';
    RAISE NOTICE 'New columns added: asset_issuer, create_template, create_contact';
    RAISE NOTICE 'Removed deprecated table: user_qr_services';
END $$;
