/**
 * SVG Sanitization utility for preventing XSS attacks in user-generated SVG content
 * This is specifically designed for owner-created templates while allowing admin templates full capabilities
 */

// Allowed SVG elements for owner templates
const ALLOWED_SVG_ELEMENTS = [
  'svg', 'g', 'path', 'rect', 'circle', 'ellipse', 'line', 'polyline', 'polygon',
  'text', 'tspan', 'textPath', 'defs', 'clipPath', 'mask', 'pattern',
  'linearGradient', 'radialGradient', 'stop', 'use', 'symbol', 'marker',
  'image', 'foreignObject'
];

// Allowed attributes for SVG elements
const ALLOWED_ATTRIBUTES = [
  // Basic SVG attributes
  'id', 'class', 'style', 'transform', 'fill', 'stroke', 'stroke-width',
  'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin',
  'opacity', 'fill-opacity', 'stroke-opacity', 'visibility', 'display',
  
  // Positioning and sizing
  'x', 'y', 'x1', 'y1', 'x2', 'y2', 'cx', 'cy', 'r', 'rx', 'ry',
  'width', 'height', 'viewBox', 'preserveAspectRatio',
  
  // Path and shape attributes
  'd', 'points', 'pathLength',
  
  // Text attributes
  'font-family', 'font-size', 'font-weight', 'font-style', 'text-anchor',
  'dominant-baseline', 'alignment-baseline', 'dx', 'dy', 'rotate',
  
  // Gradient attributes
  'gradientUnits', 'gradientTransform', 'x1', 'y1', 'x2', 'y2',
  'cx', 'cy', 'r', 'fx', 'fy', 'offset', 'stop-color', 'stop-opacity',
  
  // Pattern attributes
  'patternUnits', 'patternContentUnits', 'patternTransform',
  
  // Clip path and mask attributes
  'clipPathUnits', 'maskUnits', 'maskContentUnits',
  
  // Use element attributes
  'href', 'xlink:href',
  
  // Image attributes (restricted)
  'href', 'xlink:href'
];

// Dangerous attributes that should be removed
const DANGEROUS_ATTRIBUTES = [
  'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout', 'onmousemove',
  'onmousedown', 'onmouseup', 'onfocus', 'onblur', 'onkeydown', 'onkeyup',
  'onkeypress', 'onsubmit', 'onreset', 'onselect', 'onchange', 'onabort',
  'onunload', 'onresize', 'onscroll', 'javascript:', 'vbscript:', 'data:',
  'formaction', 'form', 'formenctype', 'formmethod', 'formnovalidate', 'formtarget'
];

// Dangerous CSS properties that should be removed from style attributes
const DANGEROUS_CSS_PROPERTIES = [
  'javascript:', 'expression(', 'behavior:', 'binding:', '-moz-binding',
  'filter:', '-ms-filter', 'zoom:', 'position:', 'absolute', 'fixed'
];

/**
 * Sanitizes SVG code to prevent XSS attacks
 * Only applies to owner-created templates (profile_email is not null)
 * Admin templates (profile_email is null) are not sanitized
 */
export function sanitizeSVG(svgCode: string, isAdminTemplate: boolean = false): string {
  // Admin templates are not sanitized - they have full capabilities
  if (isAdminTemplate) {
    return svgCode;
  }

  // Basic validation
  if (!svgCode || typeof svgCode !== 'string') {
    return '';
  }

  let sanitized = svgCode.trim();

  // Remove script tags completely
  sanitized = sanitized.replace(/<script[\s\S]*?<\/script>/gi, '');
  
  // Remove style tags with dangerous content
  sanitized = sanitized.replace(/<style[\s\S]*?<\/style>/gi, (match) => {
    // Check if style contains dangerous properties
    const hasDangerous = DANGEROUS_CSS_PROPERTIES.some(prop => 
      match.toLowerCase().includes(prop.toLowerCase())
    );
    return hasDangerous ? '' : match;
  });

  // Remove dangerous event handlers and attributes
  DANGEROUS_ATTRIBUTES.forEach(attr => {
    const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi');
    sanitized = sanitized.replace(regex, '');
  });

  // Remove javascript: and vbscript: protocols
  sanitized = sanitized.replace(/javascript:/gi, '');
  sanitized = sanitized.replace(/vbscript:/gi, '');
  
  // Remove data: URLs except for safe image formats
  sanitized = sanitized.replace(/data:(?!image\/(png|jpg|jpeg|gif|svg\+xml))[^"']*/gi, '');

  // Sanitize style attributes
  sanitized = sanitized.replace(/style\s*=\s*["']([^"']*)["']/gi, (match, styleContent) => {
    let cleanStyle = styleContent;
    
    // Remove dangerous CSS properties
    DANGEROUS_CSS_PROPERTIES.forEach(prop => {
      const regex = new RegExp(prop.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
      cleanStyle = cleanStyle.replace(regex, '');
    });
    
    // Remove expressions and functions that could be dangerous
    cleanStyle = cleanStyle.replace(/expression\s*\([^)]*\)/gi, '');
    cleanStyle = cleanStyle.replace(/url\s*\(\s*javascript:/gi, '');
    cleanStyle = cleanStyle.replace(/url\s*\(\s*vbscript:/gi, '');
    
    return `style="${cleanStyle}"`;
  });

  // Validate that it starts with <svg
  if (!sanitized.toLowerCase().startsWith('<svg')) {
    throw new Error('SVG code must start with <svg tag');
  }

  // Additional validation: ensure it's well-formed XML
  try {
    // Basic XML structure validation
    const openTags = (sanitized.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (sanitized.match(/<\/[^>]*>/g) || []).length;
    const selfClosingTags = (sanitized.match(/<[^>]*\/>/g) || []).length;
    
    // Very basic check - not perfect but catches obvious issues
    if (openTags - selfClosingTags !== closeTags) {
      throw new Error('SVG structure appears to be malformed');
    }
  } catch (error) {
    throw new Error('Invalid SVG structure');
  }

  return sanitized;
}

/**
 * Validates SVG content for basic security and structure
 */
export function validateSVGContent(svgCode: string): string[] {
  const errors: string[] = [];

  if (!svgCode || typeof svgCode !== 'string') {
    errors.push('SVG code is required');
    return errors;
  }

  const trimmed = svgCode.trim();

  if (!trimmed.toLowerCase().startsWith('<svg')) {
    errors.push('SVG code must start with <svg tag');
  }

  if (!trimmed.toLowerCase().includes('</svg>')) {
    errors.push('SVG code must end with </svg> tag');
  }

  // Check for obviously dangerous content
  if (trimmed.toLowerCase().includes('<script')) {
    errors.push('Script tags are not allowed in SVG templates');
  }

  if (/on\w+\s*=/i.test(trimmed)) {
    errors.push('Event handlers (onclick, onload, etc.) are not allowed');
  }

  if (/javascript:/i.test(trimmed)) {
    errors.push('JavaScript protocols are not allowed');
  }

  // Check size limits
  if (trimmed.length > 50000) { // 50KB limit
    errors.push('SVG code is too large (maximum 50KB)');
  }

  return errors;
}

/**
 * Checks if a template is an admin template (not user-created)
 */
export function isAdminTemplate(profileEmail: string | null): boolean {
  return profileEmail === null;
}
