import { NextRequest } from 'next/server';
import { auth } from 'auth';
import { ADMIN_EMAIL } from './config';
import { getSupabaseClient } from './supabase';
import { getUserSettings } from './user-settings';

export interface AdminAuthResult {
  isAuthorized: boolean;
  isSuperAdmin: boolean;
  isOwner: boolean;
  ownedPrimaryNames: string[];
  session: any;
  error: string | null;
  status: number;
}

/**
 * Get primary names owned by a user email
 */
async function getPrimaryNamesOwnedByUser(email: string): Promise<string[]> {
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('primary_name_owners')
      .select('owner_of')
      .eq('user_email', email);

    if (error) {
      console.error('Error fetching owned primary names:', error);
      return [];
    }

    return data?.map(row => row.owner_of) || [];
  } catch (error) {
    console.error('Error in getPrimaryNamesOwnedByUser:', error);
    return [];
  }
}

/**
 * Server-side admin authentication check
 * This function checks both the user session and admin email
 * Also checks for primary name ownership for owner-level access
 * Note: Server-side cannot check localStorage admin session,
 * so we rely on the client-side protection for the passkey verification
 */
export async function verifyAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get the current session
    const session = await auth();

    if (!session?.user?.email) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        isOwner: false,
        ownedPrimaryNames: [],
        session: null,
        error: 'No authenticated session',
        status: 401
      };
    }

    const userEmail = session.user.email;

    // Check if user is super admin
    const isSuperAdmin = userEmail === ADMIN_EMAIL;

    // Check if user has settings record (determines owner access)
    const userSettings = await getUserSettings(userEmail);
    // Owner if settings record exists AND at least one permission is enabled
    const isOwner = userSettings !== null && (
      userSettings.asset_issuer ||
      userSettings.create_template ||
      userSettings.create_contact
    );

    // Still get primary names for backward compatibility
    const ownedPrimaryNames = await getPrimaryNamesOwnedByUser(userEmail);

    // User must be either super admin or primary name owner
    if (!isSuperAdmin && !isOwner) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        isOwner: false,
        ownedPrimaryNames: [],
        session,
        error: 'Unauthorized - not admin or primary name owner',
        status: 403
      };
    }

    return {
      isAuthorized: true,
      isSuperAdmin,
      isOwner,
      ownedPrimaryNames,
      session,
      error: null,
      status: 200
    };
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      isAuthorized: false,
      isSuperAdmin: false,
      isOwner: false,
      ownedPrimaryNames: [],
      session: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Middleware function to protect admin API routes
 * Usage: const authResult = await requireAdminAuth();
 * if (!authResult.isAuthorized) return NextResponse.json({ error: authResult.error }, { status: authResult.status });
 */
export async function requireAdminAuth(request?: NextRequest): Promise<AdminAuthResult> {
  return await verifyAdminAuth(request);
}

/**
 * Helper function to check if a contact name belongs to an owner's primary name namespace
 */
export function isContactInOwnerNamespace(contactName: string, ownedPrimaryNames: string[]): boolean {
  if (!contactName || ownedPrimaryNames.length === 0) {
    return false;
  }

  // Extract the primary name from the contact name (e.g., "hello@shop" -> "shop")
  const parts = contactName.split('@');
  if (parts.length !== 2) {
    return false;
  }

  const primaryName = parts[1];
  return ownedPrimaryNames.includes(primaryName);
}

/**
 * Helper function to filter contact names by owner's namespace
 */
export function filterContactsByOwnerNamespace(contacts: any[], ownedPrimaryNames: string[]): any[] {
  return contacts.filter(contact =>
    isContactInOwnerNamespace(contact.name, ownedPrimaryNames)
  );
}
