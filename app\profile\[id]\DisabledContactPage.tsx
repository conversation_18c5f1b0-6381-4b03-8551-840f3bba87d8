'use client';

import FullLayout from 'src/components/layouts/FullLayout';
import {
  Container,
  Paper,
  Title,
  Text,
  Stack,
  Alert,
  Button,
  Group,
  Divider,
} from '@mantine/core';
import {
  IconBan,
  IconHome,
  IconMail,
} from '@tabler/icons-react';
import { useRouter } from 'next/navigation';

interface DisabledContactPageProps {
  contactName: string;
}

export default function DisabledContactPage({ contactName }: DisabledContactPageProps) {
  const router = useRouter();

  return (
    <FullLayout>
      <Container size="sm" py="xl">
        <Paper withBorder shadow="md" p="xl" radius="md">
          <Stack gap="lg" align="center">
            <div style={{ textAlign: 'center' }}>
              <IconBan size={64} color="var(--mantine-color-orange-6)" />
              <Title order={2} mt="md" mb="sm">
                Profile Temporarily Disabled
              </Title>
              <Text size="lg" c="dimmed">
                {contactName}
              </Text>
            </div>

            <Alert
              icon={<IconBan size={16} />}
              title="Access Restricted"
              color="orange"
              variant="light"
              style={{ width: '100%' }}
            >
              <Text size="sm">
                This profile has been temporarily disabled by our administrators. 
                This may be due to a policy violation, security concern, or maintenance requirement.
              </Text>
            </Alert>

            <Divider style={{ width: '100%' }} />

            <Stack gap="md" align="center">
              <Text size="sm" ta="center" c="dimmed">
                If you believe this is an error or if you are the owner of this profile, 
                please contact our support team for assistance.
              </Text>

              <Group>
                <Button
                  variant="outline"
                  leftSection={<IconHome size={16} />}
                  onClick={() => router.push('/')}
                >
                  Go Home
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconMail size={16} />}
                  onClick={() => router.push('/contact-us')}
                >
                  Contact Support
                </Button>
              </Group>
            </Stack>

            <Divider style={{ width: '100%' }} />

            <div style={{ textAlign: 'center' }}>
              <Text size="xs" c="dimmed">
                ODude Names - Identity Platform
              </Text>
            </div>
          </Stack>
        </Paper>
      </Container>
    </FullLayout>
  );
}
