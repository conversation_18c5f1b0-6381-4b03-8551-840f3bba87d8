# Ownership System Changes

This document summarizes all changes made to implement the new ownership system based on the `settings` table instead of `primary_name_owners` for feature permissions.

## Overview

The ownership dashboard now uses a dual-layer system:
1. **Namespace Ownership**: Still determined by `primary_name_owners` table
2. **Feature Permissions**: Now determined by `settings` table columns

## Database Changes

### Settings Table Updates

Added new columns to the `settings` table:
- `asset_issuer` (BOOLEAN, DEFAULT FALSE) - Controls asset creation and transaction viewing
- `create_template` (BOOLEAN, DEFAULT FALSE) - Controls template creation
- `create_contact` (BOOLEAN, DEFAULT FALSE) - Controls contact creation

### Removed Tables

- Completely removed `user_qr_services` table and all related functionality
- Updated all SQL scripts to reflect this removal

## File Changes

### Database Schema & Types
- `src/lib/database.types.ts` - Updated settings table interface
- `src/lib/user-settings.ts` - Added new permission check functions
- `scripts/fresh-new.sql` - Updated with new settings structure, removed user_qr_services
- `scripts/create-assets-tables.sql` - Updated with new settings structure
- `scripts/migrate-settings-table.sql` - New migration script for existing installations

### Owner Dashboard Updates
- `src/components/Admin/OwnerDashboard.tsx` - Added conditional action buttons based on settings

### Admin Interface Updates
- `src/components/Admin/ProfilesTable.tsx` - Added edit functionality with modal for settings

### System Updates
- `app/api/admin/system-info/route.ts` - Removed user_qr_services from required tables
- `app/admin/system-info/page.tsx` - Updated required tables list
- `assets.md` - Updated documentation

## New Features

### Owner Dashboard Action Buttons

The owner dashboard now shows conditional buttons based on user settings:

1. **Create Asset** & **View Transactions** - Shown when `asset_issuer = true`
2. **Create Template** - Shown when `create_template = true`  
3. **Create Contact** - Shown when `create_contact = true` AND user owns namespaces

### Admin Profile Management

Added edit functionality to admin profiles tab:
- Edit icon in actions column
- Modal with settings form including:
  - Max Contact Limit (number input)
  - Asset Issuer (switch)
  - Create Template (switch)
  - Create Contact (switch)

## Permission Logic

### Asset Issuer Permission
- Enables "Create Asset" button → `/owner?tab=assets`
- Enables "View Transactions" button → `/admin/points`

### Create Template Permission
- Enables "Create Template" button → `/owner?tab=templates`

### Create Contact Permission
- Enables "Create Contact" button → `/tools`
- Requires BOTH `create_contact = true` AND ownership of at least one namespace

## Migration Instructions

### For Fresh Installations
Use the updated `scripts/fresh-new.sql` which includes the new settings table structure.

### For Existing Installations
Run the migration script:
```sql
-- Run this script in your database
\i scripts/migrate-settings-table.sql
```

This will:
- Add new columns to settings table
- Create appropriate indexes
- Remove deprecated user_qr_services table
- Update table comments

## Backward Compatibility

- **NOT REQUIRED**: The user specified that backward compatibility is not required
- All user_qr_services references have been completely removed
- Fresh installations will not include the deprecated table

## Testing

After implementing these changes:
1. Test owner dashboard button visibility based on settings
2. Test admin profile edit functionality
3. Verify permission checks work correctly
4. Test migration script on a copy of production data

## Security Considerations

- Settings are only editable by super admin through the admin interface
- Permission checks are enforced at the component level
- Database constraints ensure data integrity
