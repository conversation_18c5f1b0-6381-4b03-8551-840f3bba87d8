/**
 * Client-side owner utilities
 */

import { UserSettings } from './user-settings';

export interface OwnerAccessInfo {
  isOwner: boolean;
  ownedPrimaryNames: string[];
  ownedPrimaryNamesWithTypes: {name: string, type: 'static' | 'dynamic'}[];
  userSettings?: UserSettings;
  error?: string;
}

export interface OwnerStats {
  profiles: number;
  contacts: number;
  bookmarks: number;
}

/**
 * Check owner access level for the current user
 * This does NOT require admin session verification
 */
export async function checkOwnerAccess(): Promise<OwnerAccessInfo> {
  try {
    const response = await fetch('/api/owner/access-check');

    if (!response.ok) {
      return {
        isOwner: false,
        ownedPrimaryNames: [],
        ownedPrimaryNamesWithTypes: [],
        userSettings: undefined,
        error: 'Failed to check owner access'
      };
    }

    const data = await response.json();
    return {
      isOwner: data.isOwner || false,
      ownedPrimaryNames: data.ownedPrimaryNames || [],
      ownedPrimaryNamesWithTypes: data.ownedPrimaryNamesWithTypes || [],
      userSettings: data.userSettings,
      error: data.error
    };
  } catch (error) {
    return {
      isOwner: false,
      ownedPrimaryNames: [],
      ownedPrimaryNamesWithTypes: [],
      userSettings: undefined,
      error: 'Network error checking owner access'
    };
  }
}

/**
 * Fetch owner statistics
 * This does NOT require admin session verification
 */
export async function fetchOwnerStats(): Promise<OwnerStats> {
  const response = await fetch('/api/owner/stats');

  if (!response.ok) {
    throw new Error('Failed to fetch owner stats');
  }

  const stats = await response.json();

  return {
    profiles: stats.profiles || 0,
    contacts: stats.contacts || 0,
    bookmarks: stats.bookmarks || 0,
  };
}
