import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { 
  validateEmail, 
  generateSecureToken, 
  sendPasswordResetEmail, 
  storeResetToken 
} from 'src/lib/auth-utils';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const supabase = getSupabaseAdminClient();

    // Check if user exists and has credentials auth
    const { data: user, error: checkError } = await supabase
      .from('profiles')
      .select('email, auth_provider, disabled')
      .eq('email', email.toLowerCase())
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking user:', checkError);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Always return success message for security (don't reveal if email exists)
    const successMessage = 'If an account with this email exists, you will receive a password reset link.';

    if (!user) {
      return NextResponse.json({ message: successMessage });
    }

    // Check if user is disabled
    if (user.disabled) {
      return NextResponse.json({ message: successMessage });
    }

    // Check if user has credentials auth (password)
    if (user.auth_provider === 'oauth') {
      return NextResponse.json({ message: successMessage });
    }

    // Generate reset token
    const resetToken = generateSecureToken();

    // Store reset token
    const tokenStored = await storeResetToken(email, resetToken);
    if (!tokenStored) {
      console.error('Failed to store reset token');
      return NextResponse.json(
        { error: 'Failed to process password reset request' },
        { status: 500 }
      );
    }

    // Send reset email
    const emailSent = await sendPasswordResetEmail(email, resetToken);
    if (!emailSent) {
      console.error('Failed to send password reset email');
      return NextResponse.json(
        { error: 'Failed to send password reset email' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: successMessage });

  } catch (error) {
    console.error('Forgot password API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
