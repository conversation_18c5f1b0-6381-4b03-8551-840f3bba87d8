'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Pagination,
  LoadingOverlay,
  Alert,
  Avatar,
  Group,
  Badge,
  Card,
  ActionIcon,
  ScrollArea,
  Modal,
  Stack,
  TextInput,
  NumberInput,
  Switch,
  Button,
} from '@mantine/core';
import { IconAlertCircle, IconTrash, IconBan, IconCheck, IconEdit } from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { ProfileData, fetchProfilesData, formatTimestamp, deleteProfile } from '../../lib/admin';
import { UserSettings, getUserSettings, upsertUserSettings } from '../../lib/user-settings';

interface ProfilesTableProps {
  showActions?: boolean; // Controls whether to show disable/delete actions
}

export function ProfilesTable({ showActions = true }: ProfilesTableProps) {
  const [profiles, setProfiles] = useState<ProfileData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  const ITEMS_PER_PAGE = 50;

  // Edit modal state
  const [editModalOpened, setEditModalOpened] = useState(false);
  const [editingProfile, setEditingProfile] = useState<ProfileData | null>(null);
  const [editFormData, setEditFormData] = useState({
    max_contact_limit: 4,
    asset_issuer: false,
    create_template: false,
    create_contact: false,
  });

  const fetchData = async (page: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetchProfilesData(page, ITEMS_PER_PAGE);
      
      setProfiles(response.data);
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages);
        setTotal(response.pagination.total);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch profiles');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(currentPage);
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDeleteProfile = async (profileEmail: string, profileName: string) => {
    modals.openConfirmModal({
      title: 'Delete Profile',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to delete the profile for "{profileName}" ({profileEmail})?
          This action will permanently remove:
          <br />• The user profile
          <br />• All contacts created by this user
          <br />• All bookmarks made by this user
          <br />• All point transactions for this user
          <br />• All uploaded images by this user
          <br /><br />
          <Text fw={500} c="red">
            ⚠️ This action cannot be undone and will completely remove the user from the system.
          </Text>
        </Text>
      ),
      labels: { confirm: 'Delete Profile', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteProfile(profileEmail);

          notifications.show({
            title: 'Success',
            message: 'Profile deleted successfully',
            color: 'green',
          });

          // Refresh the data
          fetchData(currentPage);
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to delete profile',
            color: 'red',
          });
        }
      },
    });
  };

  const handleToggleDisable = async (email: string, name: string, currentDisabled: boolean) => {
    const action = currentDisabled ? 'enable' : 'disable';

    modals.openConfirmModal({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Profile`,
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to {action} the profile for <strong>{name}</strong> ({email})?
          {!currentDisabled && ' Disabled profiles cannot login to the system.'}
        </Text>
      ),
      labels: { confirm: action.charAt(0).toUpperCase() + action.slice(1), cancel: 'Cancel' },
      confirmProps: { color: currentDisabled ? 'green' : 'orange' },
      onConfirm: async () => {
        try {
          const response = await fetch('/api/admin/profiles/toggle-disable', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email }),
          });

          if (response.ok) {
            const result = await response.json();
            notifications.show({
              title: 'Profile Updated',
              message: result.message,
              color: 'green',
            });
            fetchData(currentPage); // Refresh the list
          } else {
            throw new Error('Failed to toggle profile status');
          }
        } catch (error) {
          console.error('Error toggling profile status:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to update profile status. Please try again.',
            color: 'red',
          });
        }
      },
    });
  };

  const handleEditProfile = async (profile: ProfileData) => {
    setEditingProfile(profile);

    // Fetch current settings for this profile
    try {
      const settings = await getUserSettings(profile.email || '');
      setEditFormData({
        max_contact_limit: settings?.max_contact_limit || 4,
        asset_issuer: settings?.asset_issuer || false,
        create_template: settings?.create_template || false,
        create_contact: settings?.create_contact || false,
      });
    } catch (error) {
      console.error('Error fetching user settings:', error);
      // Use defaults if fetch fails
      setEditFormData({
        max_contact_limit: 4,
        asset_issuer: false,
        create_template: false,
        create_contact: false,
      });
    }

    setEditModalOpened(true);
  };

  const handleSaveSettings = async () => {
    if (!editingProfile?.email) return;

    try {
      const success = await upsertUserSettings({
        email: editingProfile.email,
        ...editFormData,
      });

      if (success) {
        notifications.show({
          title: 'Settings Updated',
          message: 'User settings have been updated successfully.',
          color: 'green',
        });
        setEditModalOpened(false);
        setEditingProfile(null);
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update settings. Please try again.',
        color: 'red',
      });
    }
  };

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  const rows = profiles.map((profile) => (
    <Table.Tr key={profile.id}>
      <Table.Td>
        <Group gap="sm">
          <Avatar src={profile.avatar_url} size={30} radius="xl" />
          <div>
            <Text size="sm" fw={500}>
              {profile.full_name || 'No name'}
            </Text>
            <Text size="xs" c="dimmed">
              {profile.email}
            </Text>
          </div>
        </Group>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{profile.email}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">
          {profile.updated_at ? formatTimestamp(profile.updated_at) : 'Never'}
        </Text>
      </Table.Td>
      <Table.Td>
        <Badge color="blue" variant="light">
          {profile.contact_count}
        </Badge>
      </Table.Td>
      <Table.Td>
        <Badge color={profile.disabled ? 'red' : 'green'} variant="light">
          {profile.disabled ? 'Disabled' : 'Active'}
        </Badge>
      </Table.Td>
      {showActions && (
        <Table.Td>
          <Group gap="xs">
            <ActionIcon
              color="blue"
              variant="light"
              onClick={() => handleEditProfile(profile)}
              title="Edit Settings"
            >
              <IconEdit size={16} />
            </ActionIcon>
            <ActionIcon
              color={profile.disabled ? 'green' : 'orange'}
              variant="light"
              onClick={() => handleToggleDisable(profile.email || '', profile.full_name || 'Unknown', profile.disabled)}
              title={profile.disabled ? 'Enable Profile' : 'Disable Profile'}
            >
              {profile.disabled ? <IconCheck size={16} /> : <IconBan size={16} />}
            </ActionIcon>
            <ActionIcon
              color="red"
              variant="light"
              onClick={() => handleDeleteProfile(profile.email || '', profile.full_name || 'Unknown')}
              title="Delete Profile"
            >
              <IconTrash size={16} />
            </ActionIcon>
          </Group>
        </Table.Td>
      )}
    </Table.Tr>
  ));

  return (
    <>
      <Card withBorder>
        <div style={{ position: 'relative' }}>
          <LoadingOverlay visible={loading} />
          
          <Text size="lg" fw={500} mb="md">
            Profiles ({total} total)
          </Text>

          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Profile</Table.Th>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Last Updated</Table.Th>
                  <Table.Th>Contacts</Table.Th>
                  <Table.Th>Status</Table.Th>
                  {showActions && <Table.Th>Actions</Table.Th>}
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {rows.length > 0 ? (
                  rows
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={showActions ? 6 : 5}>
                      <Text ta="center" c="dimmed">
                        No profiles found
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {totalPages > 1 && (
            <Group justify="center" mt="md">
              <Pagination
                value={currentPage}
                onChange={handlePageChange}
                total={totalPages}
                size="sm"
              />
            </Group>
          )}
        </div>
      </Card>

      {/* Edit Settings Modal */}
      <Modal
        opened={editModalOpened}
        onClose={() => setEditModalOpened(false)}
        title={`Edit Settings - ${editingProfile?.full_name || editingProfile?.email}`}
        centered
        size="md"
      >
        <Stack gap="md">
          <NumberInput
            label="Max Contact Limit"
            description="Maximum number of contacts this user can create"
            value={editFormData.max_contact_limit}
            onChange={(value) => setEditFormData({ ...editFormData, max_contact_limit: Number(value) || 4 })}
            min={1}
            max={100}
          />

          <Switch
            label="Asset Issuer"
            description="Allow user to create assets and view transactions"
            checked={editFormData.asset_issuer}
            onChange={(event) => setEditFormData({ ...editFormData, asset_issuer: event.currentTarget.checked })}
          />

          <Switch
            label="Create Template"
            description="Allow user to create asset templates"
            checked={editFormData.create_template}
            onChange={(event) => setEditFormData({ ...editFormData, create_template: event.currentTarget.checked })}
          />

          <Switch
            label="Create Contact"
            description="Allow user to create contacts (requires namespace ownership)"
            checked={editFormData.create_contact}
            onChange={(event) => setEditFormData({ ...editFormData, create_contact: event.currentTarget.checked })}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={() => setEditModalOpened(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveSettings}>
              Save Settings
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
}
