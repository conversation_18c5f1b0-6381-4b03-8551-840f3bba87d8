"use client"

import { 
  Container, 
  Title, 
  Text, 
  Stack, 
  Group, 
  Button, 
  Box, 
  Center,
  Transition,
  Paper,
  SimpleGrid,
  ThemeIcon,
  Anchor
} from "@mantine/core"
import { 
  IconSearch, 
  IconUserPlus, 
  IconShield, 
  IconGlobe,
  IconArrowRight,
  IconCoins,
  IconGift
} from "@tabler/icons-react"
import { ODudeNameSearch } from "../Search"
import { useState, useEffect } from "react"
import { SIGNUP_POINT, USD_POINT_RATE } from "../../lib/config"
import Link from "next/link"
import styles from "./index.module.css"

const features = [
  {
    icon: IconSearch,
    title: "Discover People",
    description: "Search and find people by their ODude names instantly"
  },
  {
    icon: IconUserPlus,
    title: "Create Contacts",
    description: "Build your network with ODude's contact management"
  },
  {
    icon: IconShield,
    title: "Secure & Private",
    description: "Your data is protected with blockchain technology"
  },
  {
    icon: IconGlobe,
    title: "Global Network",
    description: "Connect with people worldwide through ODude names"
  }
]

export const LandingPage = () => {
  const [showTitle, setShowTitle] = useState(false)
  const [showSubtitle, setShowSubtitle] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const [showButtons, setShowButtons] = useState(false)
  const [showIncentive, setShowIncentive] = useState(false)
  const [showFeatures, setShowFeatures] = useState(false)

  useEffect(() => {
    // Stagger the animations with timeouts
    const timers = [
      setTimeout(() => setShowTitle(true), 100),
      setTimeout(() => setShowSubtitle(true), 300),
      setTimeout(() => setShowSearch(true), 500),
      setTimeout(() => setShowButtons(true), 700),
      setTimeout(() => setShowIncentive(true), 900),
      setTimeout(() => setShowFeatures(true), 1100)
    ]

    return () => {
      timers.forEach(timer => clearTimeout(timer))
    }
  }, [])

  return (
    <Box>
      {/* Hero Section */}
      <Container size="lg" py={80} className={styles.heroSection}>
        <Center>
          <Stack align="center" gap="xl" maw={600}>
            <Transition
              mounted={showTitle}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
            >
              {(transitionStyles) => (
                <div style={transitionStyles}>
                  <Title
                    order={1}
                    size="3.5rem"
                    fw={200}
                    ta="center"
                    mb="sm"
                  >
                    ODude
                  </Title>
                </div>
              )}
            </Transition>

            <Transition
              mounted={showSubtitle}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
            >
              {(transitionStyles) => (
                <div style={transitionStyles}>
                  <Text size="xl" c="dimmed" ta="center" maw={500}>
                    Discover, connect, and manage your identity
                  </Text>
                </div>
              )}
            </Transition>

            <Transition
              mounted={showSearch}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
            >
              {(transitionStyles) => (
                <Box style={transitionStyles} w="100%" maw={500}>
                  <ODudeNameSearch
                    size="lg"
                    placeholder="Search for an ODude name..."
                  />
                </Box>
              )}
            </Transition>

            <Transition
              mounted={showButtons}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
            >
              {(transitionStyles) => (
                <Group style={transitionStyles} gap="md">
                  <Button
                    component={Link}
                    href="/auth/signin"
                    size="lg"
                    radius="xl"
                    variant="gradient"
                    gradient={{ from: 'blue', to: 'cyan' }}
                    leftSection={<IconUserPlus size={20} />}
                    rightSection={<IconArrowRight size={16} />}
                  >
                    Login
                  </Button>
                  <Button
                    component="a"
                    href="https://odude.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    variant="outline"
                    size="lg"
                    radius="xl"
                    color="gray"
                  >
                    Learn More
                  </Button>
                </Group>
              )}
            </Transition>

            {/* Signup Incentive */}
            <Transition
              mounted={showIncentive}
              transition="fade"
              duration={1000}
            >
              {(transitionStyles) => (
                <Paper
                  style={transitionStyles}
                  p="md"
                  radius="lg"
                  withBorder
                >
                  <Group gap="xs" justify="center">
                    <ThemeIcon variant="light" color="blue" size="sm">
                      <IconGift size={16} />
                    </ThemeIcon>
                    <Text size="sm" fw={500}>
                      Get {SIGNUP_POINT} points (${SIGNUP_POINT * USD_POINT_RATE}) when you sign up!
                    </Text>
                  </Group>
                </Paper>
              )}
            </Transition>
          </Stack>
        </Center>
      </Container>

      {/* Features Section */}
      <Container size="lg" py={60}>
        <Transition
          mounted={showFeatures}
          transition="slide-up"
          duration={800}
        >
          {(transitionStyles) => (
            <div style={transitionStyles}>
              <Title order={2} ta="center" mb="xl" fw={300}>
                Why Choose ODude?
              </Title>
              <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="xl">
                {features.map((feature, index) => (
                  <Paper
                    key={index}
                    p="xl"
                    radius="lg"
                    withBorder
                  >
                    <Stack align="center" gap="md">
                      <ThemeIcon
                        size={60}
                        radius="xl"
                        variant="gradient"
                        gradient={{ from: 'blue', to: 'cyan' }}
                      >
                        <feature.icon size={30} />
                      </ThemeIcon>
                      <Title order={4} ta="center" fw={500}>
                        {feature.title}
                      </Title>
                      <Text size="sm" c="dimmed" ta="center">
                        {feature.description}
                      </Text>
                    </Stack>
                  </Paper>
                ))}
              </SimpleGrid>
            </div>
          )}
        </Transition>
      </Container>
    </Box>
  )
}
