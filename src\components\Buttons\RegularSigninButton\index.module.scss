.regular_button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 24px;
  margin: 8px 0;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background-color: #ffffff;
  color: #495057;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  
  &:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  }
  
  svg {
    margin-right: 8px;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .regular_button {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
    
    &:hover {
      background-color: #4a5568;
      border-color: #718096;
      color: #f7fafc;
    }
    
    &:focus {
      border-color: #4285f4;
      box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .regular_button {
    font-size: 14px;
    padding: 10px 20px;
  }
}
