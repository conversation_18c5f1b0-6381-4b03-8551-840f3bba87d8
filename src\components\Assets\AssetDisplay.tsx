'use client';

import { useState, useEffect } from 'react';
import { Image, Box } from '@mantine/core';
import { fetchData } from '../../lib/supabase';
import { AssetTemplate } from '../../lib/assets-utils';

interface AssetDisplayProps {
  imageUrl: string;
  templateId?: string | null;
  title: string;
  width?: number;
  height?: number;
  fit?: 'contain' | 'cover' | 'fill' | 'scale-down';
  // Asset data for placeholder replacement
  assetData?: {
    title?: string;
    description?: string;
    issued_date?: string;
    expire_date?: string;
    full_name?: string; // Deprecated - use receiver_full_name
    profile_name?: string; // Deprecated - use issuer_profile_name
    profile_avatar?: string; // Deprecated - use issuer_profile_avatar
    asset_image?: string;
    // New placeholders
    receiver_full_name?: string;
    receiver_profile_avatar?: string;
    issuer_profile_name?: string;
    issuer_profile_avatar?: string;
    issuer_full_name?: string;
  };
}

export default function AssetDisplay({
  imageUrl,
  templateId,
  title,
  width = 200,
  height = 200,
  fit = 'cover',
  assetData
}: AssetDisplayProps) {
  const [template, setTemplate] = useState<AssetTemplate | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      if (!templateId) return;

      try {
        const { data, error } = await fetchData('asset_templates', {
          select: '*',
          filter: [{ column: 'id', value: templateId }],
          single: true
        });

        if (!error && data) {
          const templateData = Array.isArray(data) ? data[0] : data;
          setTemplate(templateData);
        }
      } catch (error) {
        console.error('Error fetching template:', error);
      }
    };

    fetchTemplate();
  }, [templateId]);

  // Function to replace placeholders in SVG code
  const replacePlaceholders = (svgCode: string): string => {
    if (!assetData) return svgCode;

    let processedSvg = svgCode;

    // Replace all supported placeholders
    const placeholders = {
      '{{issued_date}}': assetData.issued_date || new Date().toLocaleDateString(),
      '{{expire_date}}': assetData.expire_date || 'No expiration',
      '{{asset_image}}': assetData.asset_image || imageUrl,
      '{{asset_description}}': assetData.description || '',
      '{{asset_title}}': assetData.title || title,
      // New placeholders
      '{{receiver_full_name}}': assetData.receiver_full_name || assetData.full_name || 'Recipient Name',
      '{{receiver_profile_avatar}}': assetData.receiver_profile_avatar || '',
      '{{issuer_profile_name}}': assetData.issuer_profile_name || assetData.profile_name || 'Issuer Name',
      '{{issuer_profile_avatar}}': assetData.issuer_profile_avatar || assetData.profile_avatar || '',
      '{{issuer_full_name}}': assetData.issuer_full_name || 'Issuer Full Name'
    };

    Object.entries(placeholders).forEach(([placeholder, value]) => {
      processedSvg = processedSvg.replace(new RegExp(placeholder, 'g'), value);
    });

    return processedSvg;
  };

  // If no template, just show the raw image
  if (!templateId || !template) {
    return (
      <Image
        src={imageUrl}
        alt={title}
        width={width}
        height={height}
        fit={fit}
      />
    );
  }

  // If template exists and has SVG code, render the SVG with placeholders replaced
  if (template.svg_code) {
    const processedSvg = replacePlaceholders(template.svg_code);

    return (
      <Box style={{ width, height }}>
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          dangerouslySetInnerHTML={{ __html: processedSvg }}
        />
      </Box>
    );
  }

  // If no SVG code, show just the image
  return (
    <Image
      src={imageUrl}
      alt={title}
      width={width}
      height={height}
      fit={fit}
    />
  );
}
