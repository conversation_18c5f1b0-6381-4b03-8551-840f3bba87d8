/* Login Page Animations */
.loginContainer {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loginPaper {
  transition: all 0.3s ease;
  border: 1px solid var(--mantine-color-gray-3);
  background: var(--mantine-color-body);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.loginPaper:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.gradientTitle {
  background: linear-gradient(45deg, var(--mantine-color-blue-6), var(--mantine-color-cyan-5));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleShimmer 2s ease-in-out infinite alternate;
}

@keyframes titleShimmer {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.2);
  }
}

.signInButton {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.signInButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.signInButton:hover::before {
  left: 100%;
}

.signInButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.incentiveCard {
  background: linear-gradient(135deg, var(--mantine-color-blue-0), var(--mantine-color-cyan-0));
  border: 1px solid var(--mantine-color-blue-2);
  animation: glow 3s ease-in-out infinite alternate;
  transition: all 0.3s ease;
}

.incentiveCard:hover {
  transform: scale(1.02);
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(34, 139, 230, 0.2);
  }
  100% {
    box-shadow: 0 0 20px rgba(34, 139, 230, 0.4);
  }
}

.backButton {
  transition: all 0.3s ease;
}

.backButton:hover {
  transform: translateX(-4px);
  color: var(--mantine-color-blue-6);
}

.errorAlert {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loadingSpinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark theme adjustments */
[data-mantine-color-scheme="dark"] .loginPaper {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

[data-mantine-color-scheme="dark"] .loginPaper:hover {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

[data-mantine-color-scheme="dark"] .incentiveCard {
  background: linear-gradient(135deg, var(--mantine-color-dark-5), var(--mantine-color-dark-4));
  border-color: var(--mantine-color-blue-4);
}

[data-mantine-color-scheme="dark"] .signInButton:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .loginContainer {
    min-height: 70vh;
    padding: 1rem;
  }
  
  .loginPaper {
    margin: 1rem;
  }
  
  .gradientTitle {
    font-size: 1.8rem !important;
  }
}

/* Focus states for accessibility */
.signInButton:focus-visible {
  outline: 2px solid var(--mantine-color-blue-5);
  outline-offset: 2px;
}

.backButton:focus-visible {
  outline: 2px solid var(--mantine-color-gray-5);
  outline-offset: 2px;
}

/* Smooth transitions for theme switching */
.smoothTransition {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
