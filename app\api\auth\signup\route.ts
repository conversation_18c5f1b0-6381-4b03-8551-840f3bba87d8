import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { 
  hashPassword, 
  validatePassword, 
  validateEmail, 
  generateSecureToken, 
  sendVerificationEmail, 
  storeVerificationToken 
} from 'src/lib/auth-utils';
import { awardSignupPoints } from 'src/lib/points';

export async function POST(request: NextRequest) {
  try {
    const { email, password, fullName } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: 'Password validation failed', details: passwordValidation.errors },
        { status: 400 }
      );
    }

    const supabase = getSupabaseAdminClient();

    // Check if user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('profiles')
      .select('email, auth_provider')
      .eq('email', email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing user:', checkError);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    if (existingUser) {
      if (existingUser.auth_provider === 'oauth') {
        return NextResponse.json(
          { error: 'An account with this email already exists. Please sign in with your social account or use password reset.' },
          { status: 409 }
        );
      } else {
        return NextResponse.json(
          { error: 'An account with this email already exists' },
          { status: 409 }
        );
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Generate verification token
    const verificationToken = generateSecureToken();

    // Create user profile
    const { data: newUser, error: createError } = await supabase
      .from('profiles')
      .insert({
        email: email.toLowerCase(),
        full_name: fullName || null,
        password_hash: hashedPassword,
        email_verified: false,
        auth_provider: 'credentials',
        points: 2000, // Starting signup bonus
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating user:', createError);
      return NextResponse.json(
        { error: 'Failed to create account' },
        { status: 500 }
      );
    }

    // Store verification token
    const tokenStored = await storeVerificationToken(email, verificationToken);
    if (!tokenStored) {
      console.error('Failed to store verification token');
      // Don't fail the signup, but log the error
    }

    // Send verification email
    const emailSent = await sendVerificationEmail(email, verificationToken);
    if (!emailSent) {
      console.error('Failed to send verification email');
      // Don't fail the signup, but log the error
    }

    // Award signup points (this will also initialize user points)
    try {
      const pointsAwarded = await awardSignupPoints(email);
      if (pointsAwarded) {
        console.log('Signup points awarded successfully to:', email);
      } else {
        console.error('Failed to award signup points to:', email);
      }
    } catch (pointsError) {
      console.error('Error with point system during signup:', pointsError);
    }

    return NextResponse.json({
      message: 'Account created successfully. Please check your email to verify your account.',
      user: {
        id: newUser.id,
        email: newUser.email,
        fullName: newUser.full_name,
        emailVerified: newUser.email_verified,
      },
    });

  } catch (error) {
    console.error('Signup API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
