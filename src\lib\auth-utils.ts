import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import nodemailer from 'nodemailer';
import { getSupabaseAdminClient } from './supabaseAdmin';

// Password validation constants
export const PASSWORD_MIN_LENGTH = 8;
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;

// Token expiration times (in milliseconds)
export const VERIFICATION_TOKEN_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
export const RESET_TOKEN_EXPIRY = 1 * 60 * 60 * 1000; // 1 hour

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Generate a secure random token
 */
export function generateSecureToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < PASSWORD_MIN_LENGTH) {
    errors.push(`Password must be at least ${PASSWORD_MIN_LENGTH} characters long`);
  }

  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Create SMTP transporter
 */
function createTransporter() {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '465'),
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
}

/**
 * Send verification email
 */
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    const transporter = createTransporter();
    const verificationUrl = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${token}`;

    const mailOptions = {
      from: process.env.SMTP_USER,
      to: email,
      subject: 'Verify your ODude account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Welcome to ODude!</h2>
          <p>Thank you for signing up. Please verify your email address by clicking the button below:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #4285f4; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          
          <p style="margin-top: 30px; color: #666; font-size: 14px;">
            This verification link will expire in 24 hours. If you didn't create an account with ODude, you can safely ignore this email.
          </p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px;">
            This email was sent from ODude Names. Please do not reply to this email.
          </p>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
  try {
    const transporter = createTransporter();
    const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${token}`;

    const mailOptions = {
      from: process.env.SMTP_USER,
      to: email,
      subject: 'Reset your ODude password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p>You requested to reset your password for your ODude account. Click the button below to set a new password:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          
          <p style="margin-top: 30px; color: #666; font-size: 14px;">
            This reset link will expire in 1 hour. If you didn't request a password reset, you can safely ignore this email.
          </p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px;">
            This email was sent from ODude Names. Please do not reply to this email.
          </p>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return false;
  }
}

/**
 * Store verification token in database
 */
export async function storeVerificationToken(email: string, token: string): Promise<boolean> {
  try {
    const supabase = getSupabaseAdminClient();
    const expiresAt = new Date(Date.now() + VERIFICATION_TOKEN_EXPIRY);

    const { error } = await supabase
      .from('profiles')
      .update({
        verification_token: token,
        verification_token_expires: expiresAt.toISOString(),
      })
      .eq('email', email);

    return !error;
  } catch (error) {
    console.error('Error storing verification token:', error);
    return false;
  }
}

/**
 * Store password reset token in database
 */
export async function storeResetToken(email: string, token: string): Promise<boolean> {
  try {
    const supabase = getSupabaseAdminClient();
    const expiresAt = new Date(Date.now() + RESET_TOKEN_EXPIRY);

    const { error } = await supabase
      .from('profiles')
      .update({
        reset_token: token,
        reset_token_expires: expiresAt.toISOString(),
      })
      .eq('email', email);

    return !error;
  } catch (error) {
    console.error('Error storing reset token:', error);
    return false;
  }
}

/**
 * Verify and consume verification token
 */
export async function verifyEmailToken(token: string): Promise<{ success: boolean; email?: string; error?: string }> {
  try {
    const supabase = getSupabaseAdminClient();

    const { data, error } = await supabase
      .from('profiles')
      .select('email, verification_token_expires')
      .eq('verification_token', token)
      .single();

    if (error || !data) {
      return { success: false, error: 'Invalid verification token' };
    }

    // Check if token has expired
    if (new Date() > new Date(data.verification_token_expires)) {
      return { success: false, error: 'Verification token has expired' };
    }

    // Mark email as verified and clear token
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        email_verified: true,
        verification_token: null,
        verification_token_expires: null,
      })
      .eq('verification_token', token);

    if (updateError) {
      return { success: false, error: 'Failed to verify email' };
    }

    return { success: true, email: data.email };
  } catch (error) {
    console.error('Error verifying email token:', error);
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Verify reset token and return email if valid
 */
export async function verifyResetToken(token: string): Promise<{ success: boolean; email?: string; error?: string }> {
  try {
    const supabase = getSupabaseAdminClient();

    const { data, error } = await supabase
      .from('profiles')
      .select('email, reset_token_expires')
      .eq('reset_token', token)
      .single();

    if (error || !data) {
      return { success: false, error: 'Invalid reset token' };
    }

    // Check if token has expired
    if (new Date() > new Date(data.reset_token_expires)) {
      return { success: false, error: 'Reset token has expired' };
    }

    return { success: true, email: data.email };
  } catch (error) {
    console.error('Error verifying reset token:', error);
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Update password and clear reset token
 */
export async function updatePasswordWithToken(token: string, newPassword: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = getSupabaseAdminClient();

    // First verify the token is still valid
    const tokenResult = await verifyResetToken(token);
    if (!tokenResult.success) {
      return tokenResult;
    }

    // Hash the new password
    const hashedPassword = await hashPassword(newPassword);

    // Update password and clear reset token
    const { error } = await supabase
      .from('profiles')
      .update({
        password_hash: hashedPassword,
        reset_token: null,
        reset_token_expires: null,
        auth_provider: 'both', // User now has both OAuth and credentials
      })
      .eq('reset_token', token);

    if (error) {
      return { success: false, error: 'Failed to update password' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating password:', error);
    return { success: false, error: 'Internal server error' };
  }
}
