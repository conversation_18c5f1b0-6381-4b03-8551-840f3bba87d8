import { Suspense } from 'react';
import { Container, Paper, Title, Text, Button, Alert } from '@mantine/core';
import { IconCheck, IconX, IconMail } from '@tabler/icons-react';
import Link from 'next/link';

interface VerifyEmailPageProps {
  searchParams: { token?: string };
}

async function verifyEmailToken(token: string) {
  try {
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/auth/verify-email?token=${token}`, {
      method: 'GET',
    });
    
    const data = await response.json();
    
    return {
      success: response.ok,
      message: data.message,
      error: data.error,
      email: data.email,
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to verify email. Please try again.',
    };
  }
}

function VerifyEmailContent({ searchParams }: VerifyEmailPageProps) {
  const token = searchParams.token;

  if (!token) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <IconX size={48} color="red" style={{ marginBottom: 16 }} />
            <Title order={2} mb="md">Invalid Verification Link</Title>
            <Text c="dimmed" mb="xl">
              The verification link is invalid or missing. Please check your email for the correct link.
            </Text>
            <Button component={Link} href="/" variant="filled">
              Go to Home
            </Button>
          </div>
        </Paper>
      </Container>
    );
  }

  return <VerifyEmailResult token={token} />;
}

async function VerifyEmailResult({ token }: { token: string }) {
  const result = await verifyEmailToken(token);

  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md">
        <div style={{ textAlign: 'center' }}>
          {result.success ? (
            <>
              <IconCheck size={48} color="green" style={{ marginBottom: 16 }} />
              <Title order={2} mb="md">Email Verified Successfully!</Title>
              <Text c="dimmed" mb="xl">
                Your email address has been verified. You can now sign in to your account.
              </Text>
              <Alert icon={<IconMail size={16} />} color="green" mb="xl">
                Welcome to ODude! Your account is now active.
              </Alert>
              <Button component={Link} href="/" variant="filled">
                Sign In Now
              </Button>
            </>
          ) : (
            <>
              <IconX size={48} color="red" style={{ marginBottom: 16 }} />
              <Title order={2} mb="md">Verification Failed</Title>
              <Text c="dimmed" mb="xl">
                {result.error || 'The verification link is invalid or has expired.'}
              </Text>
              <Alert icon={<IconX size={16} />} color="red" mb="xl">
                Please request a new verification email or contact support if the problem persists.
              </Alert>
              <Button component={Link} href="/" variant="filled">
                Go to Home
              </Button>
            </>
          )}
        </div>
      </Paper>
    </Container>
  );
}

export default function VerifyEmailPage({ searchParams }: VerifyEmailPageProps) {
  return (
    <Suspense fallback={
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <Text>Verifying your email...</Text>
          </div>
        </Paper>
      </Container>
    }>
      <VerifyEmailContent searchParams={searchParams} />
    </Suspense>
  );
}
