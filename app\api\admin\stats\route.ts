import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const supabase = getSupabaseAdminClient();

    let profilesCount = 0;
    let contactsCount = 0;
    let bookmarksCount = 0;
    let assetTemplatesCount = 0;

    if (authResult.isSuperAdmin) {
      // Super admin sees all stats
      const [profilesResult, contactsResult, bookmarksResult, assetTemplatesResult] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('contact').select('*', { count: 'exact', head: true }),
        supabase.from('bookmark').select('*', { count: 'exact', head: true }),
        supabase.from('asset_templates').select('*', { count: 'exact', head: true })
      ]);

      if (profilesResult.error) {
        console.error('Error fetching profiles count:', profilesResult.error);
        return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
      }

      if (contactsResult.error) {
        console.error('Error fetching contacts count:', contactsResult.error);
        return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
      }

      if (bookmarksResult.error) {
        console.error('Error fetching bookmarks count:', bookmarksResult.error);
        return NextResponse.json({ error: 'Failed to fetch bookmarks count' }, { status: 500 });
      }

      if (assetTemplatesResult.error) {
        console.error('Error fetching asset templates count:', assetTemplatesResult.error);
        return NextResponse.json({ error: 'Failed to fetch asset templates count' }, { status: 500 });
      }

      profilesCount = profilesResult.count || 0;
      contactsCount = contactsResult.count || 0;
      bookmarksCount = bookmarksResult.count || 0;
      assetTemplatesCount = assetTemplatesResult.count || 0;
    } else if (authResult.isOwner) {
      // Owner sees only their namespace stats

      // Get contacts in their namespace
      const namespaceConditions = authResult.ownedPrimaryNames.map(primaryName =>
        `name.like.%@${primaryName}`
      ).join(',');

      if (namespaceConditions) {
        const { count: ownerContactsCount, error: contactsError } = await supabase
          .from('contact')
          .select('*', { count: 'exact', head: true })
          .or(namespaceConditions);

        if (contactsError) {
          console.error('Error fetching owner contacts count:', contactsError);
          return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
        }

        contactsCount = ownerContactsCount || 0;

        // Get profiles that have contacts in their namespace
        const { data: contactsData, error: contactsDataError } = await supabase
          .from('contact')
          .select('profile_email')
          .or(namespaceConditions);

        if (contactsDataError) {
          console.error('Error fetching contacts data for profiles count:', contactsDataError);
          return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
        }

        // Count unique profile emails
        const uniqueProfileEmails = new Set(contactsData?.map(c => c.profile_email).filter(Boolean));
        profilesCount = uniqueProfileEmails.size;

        // Get bookmarks for contacts in their namespace
        const contactNames = (await supabase
          .from('contact')
          .select('name')
          .or(namespaceConditions)).data?.map(c => c.name) || [];

        if (contactNames.length > 0) {
          const { count: ownerBookmarksCount, error: bookmarksError } = await supabase
            .from('bookmark')
            .select('*', { count: 'exact', head: true })
            .in('contact_name', contactNames);

          if (bookmarksError) {
            console.error('Error fetching owner bookmarks count:', bookmarksError);
            return NextResponse.json({ error: 'Failed to fetch bookmarks count' }, { status: 500 });
          }

          bookmarksCount = ownerBookmarksCount || 0;
        }
      }
    }

    return NextResponse.json({
      profiles: profilesCount,
      contacts: contactsCount,
      bookmarks: bookmarksCount,
      assetTemplates: assetTemplatesCount,
    });

  } catch (error) {
    console.error('Admin stats API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
