import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { getUserSettings } from 'src/lib/user-settings';

/**
 * Get primary names owned by a user with their ownership types
 */
async function getPrimaryNamesOwnedByUser(userEmail: string): Promise<{name: string, type: 'static' | 'dynamic'}[]> {
  const supabase = getSupabaseClient();

  const { data: owners, error } = await supabase
    .from('primary_name_owners')
    .select('owner_of, ownership_type')
    .eq('user_email', userEmail);

  if (error) {
    console.error('Error fetching primary name owners:', error);
    return [];
  }

  return owners?.map(owner => ({
    name: owner.owner_of,
    type: owner.ownership_type || 'static'
  })) || [];
}

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json({
        isOwner: false,
        ownedPrimaryNames: [],
        error: 'No authenticated session'
      }, { status: 401 });
    }

    const userEmail = session.user.email;

    // Check if user has a record in settings table (determines owner access)
    const userSettings = await getUserSettings(userEmail);
    // Owner if settings record exists AND at least one permission is enabled
    const isOwner = userSettings !== null && (
      userSettings.asset_issuer ||
      userSettings.create_template ||
      userSettings.create_contact
    );

    // Still get primary names for backward compatibility and display purposes
    const ownedPrimaryNamesWithTypes = await getPrimaryNamesOwnedByUser(userEmail);
    const ownedPrimaryNames = ownedPrimaryNamesWithTypes.map(item => item.name);

    return NextResponse.json({
      isOwner,
      ownedPrimaryNames,
      ownedPrimaryNamesWithTypes,
      userSettings: userSettings || {
        email: userEmail,
        max_contact_limit: 4,
        asset_issuer: false,
        create_template: false,
        create_contact: false,
        created_at: '',
        updated_at: ''
      },
      error: null
    });
  } catch (error) {
    console.error('Owner access check API error:', error);
    return NextResponse.json({
      isOwner: false,
      ownedPrimaryNames: [],
      error: 'Internal server error'
    }, { status: 500 });
  }
}
