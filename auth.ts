import NextAuth from "next-auth"
import Facebook from "next-auth/providers/facebook"
import GitHub from "next-auth/providers/github"
import Google from "next-auth/providers/google"
import Credentials from "next-auth/providers/credentials"
import { insertData, getDataValue } from "./src/lib/supabase"
import { initializeUserPoints, awardSignupPoints } from "./src/lib/points"
import { verifyPassword, validateEmail } from "./src/lib/auth-utils"
import { getSupabaseAdminClient } from "./src/lib/supabaseAdmin"

export const { auth, handlers } = NextAuth({
  providers: [
    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID ?? "",
      clientSecret: process.env.GITHUB_CLIENT_SECRET ?? "",
    }),
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID ?? "",
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET ?? "",
    }),
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            return null;
          }

          const email = credentials.email as string;
          const password = credentials.password as string;

          // Validate email format
          if (!validateEmail(email)) {
            return null;
          }

          const supabase = getSupabaseAdminClient();

          // Get user from database
          const { data: user, error } = await supabase
            .from('profiles')
            .select('id, email, full_name, avatar_url, password_hash, email_verified, disabled, auth_provider')
            .eq('email', email)
            .single();

          if (error || !user) {
            return null;
          }

          // Check if user is disabled
          if (user.disabled) {
            return null;
          }

          // Check if user has a password (credentials auth enabled)
          if (!user.password_hash) {
            return null;
          }

          // Check if email is verified
          if (!user.email_verified) {
            return null;
          }

          // Verify password
          const isValidPassword = await verifyPassword(password, user.password_hash);
          if (!isValidPassword) {
            return null;
          }

          // Return user object for NextAuth
          return {
            id: user.id,
            email: user.email,
            name: user.full_name,
            image: user.avatar_url,
          };
        } catch (error) {
          console.error('Credentials authorization error:', error);
          return null;
        }
      }
    }),
  ],
  secret: process.env.NEXT_AUTH_SECRET,
  pages: {
    signIn: '/auth/signin',
    error: '/auth/signin',
  },
  callbacks: {
    async signIn({ user, account }) {
      try {
        // Check if user already exists in profiles table
        if (user.email) {
          const existingData = await getDataValue('profiles', 'email', user.email, 'full_name');

          // If user doesn't exist, create profile entry (OAuth only - credentials users are created via signup API)
          if (existingData === null && account?.provider !== 'credentials') {
            const { data, error } = await insertData('profiles', {
              full_name: user.name || '',
              email: user.email,
              avatar_url: user.image || '',
              email_verified: true, // OAuth providers verify emails
              auth_provider: 'oauth'
            });

            if (error) {
              console.error('Error creating user profile:', error);
            } else {
              console.log('User profile created successfully:', data);

              // Award signup bonus (this will also initialize user points)
              try {
                const pointsAwarded = await awardSignupPoints(user.email);
                if (pointsAwarded) {
                  console.log('Signup points awarded successfully to:', user.email);
                } else {
                  console.error('Failed to award signup points to:', user.email);
                }
              } catch (pointsError) {
                console.error('Error with point system during signup:', pointsError);
              }
            }
          } else if (existingData !== null) {
            // User exists, check if they are disabled
            const disabledStatus = await getDataValue('profiles', 'email', user.email, 'disabled');
            if (disabledStatus === true) {
              console.log('Login blocked for disabled user:', user.email);
              return false; // Block login for disabled users
            }

            // For OAuth login on existing credentials account, update auth_provider to 'both'
            if (account?.provider !== 'credentials') {
              const currentAuthProvider = await getDataValue('profiles', 'email', user.email, 'auth_provider');
              if (currentAuthProvider === 'credentials') {
                const supabase = getSupabaseAdminClient();
                await supabase
                  .from('profiles')
                  .update({
                    auth_provider: 'both',
                    email_verified: true // OAuth providers verify emails
                  })
                  .eq('email', user.email);
              }
            }
          }
        }
        return true;
      } catch (error) {
        console.error('Error in signIn callback:', error);
        return true; // Allow signin even if profile creation fails
      }
    },
  },
})
