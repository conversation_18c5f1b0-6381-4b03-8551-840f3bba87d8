import { LandingPage } from "src/components/LandingPage"
import LandingLayout from 'src/components/layouts/LandingLayout'
import FullLayout from 'src/components/layouts/FullLayout'
import { HomePage } from "src/components/HomePage"
import { auth } from "auth"

export default async function Page() {
  const session = await auth()

  if (session) {
    // Authenticated users get the full layout with sidebar
    return (
      <FullLayout>
        <HomePage />
      </FullLayout>
    )
  }

  // Unauthenticated users get the landing layout
  return (
    <LandingLayout>
      <LandingPage />
    </LandingLayout>
  )
}
