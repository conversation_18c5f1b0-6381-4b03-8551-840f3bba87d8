import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage owners
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const supabase = getSupabaseAdminClient();

    const { data: owners, error } = await supabase
      .from('primary_name_owners')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching owners:', error);
      return NextResponse.json({ error: 'Failed to fetch owners' }, { status: 500 });
    }

    return NextResponse.json(owners || []);
  } catch (error) {
    console.error('Admin owner GET API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage owners
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { user_email, owner_of, ownership_type = 'static', minted = false } = await request.json();

    if (!user_email || !owner_of) {
      return NextResponse.json({ error: 'User email and primary name are required' }, { status: 400 });
    }

    // Validate ownership_type
    if (ownership_type && !['static', 'dynamic'].includes(ownership_type)) {
      return NextResponse.json({ error: 'Invalid ownership type. Must be "static" or "dynamic"' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(user_email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Check if this combination already exists
    const { data: existing, error: checkError } = await supabase
      .from('primary_name_owners')
      .select('*')
      .eq('user_email', user_email)
      .eq('owner_of', owner_of)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing owner:', checkError);
      return NextResponse.json({ error: 'Failed to check existing owner' }, { status: 500 });
    }

    if (existing) {
      return NextResponse.json({ error: 'This user already owns this primary name' }, { status: 400 });
    }

    // Insert new owner
    const { data: newOwner, error: insertError } = await supabase
      .from('primary_name_owners')
      .insert({
        user_email,
        owner_of,
        ownership_type,
        minted,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating owner:', insertError);
      return NextResponse.json({ error: 'Failed to create owner' }, { status: 500 });
    }

    return NextResponse.json(newOwner);
  } catch (error) {
    console.error('Admin owner POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage owners
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { id, user_email, owner_of, ownership_type = 'static', minted = false } = await request.json();

    if (!id || !user_email || !owner_of) {
      return NextResponse.json({ error: 'ID, user email and primary name are required' }, { status: 400 });
    }

    // Validate ownership_type
    if (ownership_type && !['static', 'dynamic'].includes(ownership_type)) {
      return NextResponse.json({ error: 'Invalid ownership type. Must be "static" or "dynamic"' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(user_email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Check if this combination already exists (excluding current record)
    const { data: existing, error: checkError } = await supabase
      .from('primary_name_owners')
      .select('*')
      .eq('user_email', user_email)
      .eq('owner_of', owner_of)
      .neq('id', id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing owner:', checkError);
      return NextResponse.json({ error: 'Failed to check existing owner' }, { status: 500 });
    }

    if (existing) {
      return NextResponse.json({ error: 'This user already owns this primary name' }, { status: 400 });
    }

    // Update owner
    const { data: updatedOwner, error: updateError } = await supabase
      .from('primary_name_owners')
      .update({
        user_email,
        owner_of,
        ownership_type,
        minted,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating owner:', updateError);
      return NextResponse.json({ error: 'Failed to update owner' }, { status: 500 });
    }

    return NextResponse.json(updatedOwner);
  } catch (error) {
    console.error('Admin owner PUT API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can manage owners
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - super admin access required' }, { status: 403 });
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Owner ID is required' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    const { error: deleteError } = await supabase
      .from('primary_name_owners')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting owner:', deleteError);
      return NextResponse.json({ error: 'Failed to delete owner' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Admin owner DELETE API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
