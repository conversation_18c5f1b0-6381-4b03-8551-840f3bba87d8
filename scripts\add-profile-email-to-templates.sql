-- Migration script to add profile_email column to asset_templates table
-- This allows templates to be created by owners and isolated to their profile

-- Add profile_email column to asset_templates table
ALTER TABLE asset_templates 
ADD COLUMN profile_email TEXT;

-- Add comment to explain the column
COMMENT ON COLUMN asset_templates.profile_email IS 'NULL for admin templates, email for owner-created templates';

-- Create index for better performance when filtering by profile_email
CREATE INDEX IF NOT EXISTS idx_asset_templates_profile_email ON asset_templates(profile_email);

-- Update existing templates to have NULL profile_email (admin templates)
-- This is already the default for new columns, but making it explicit
UPDATE asset_templates SET profile_email = NULL WHERE profile_email IS NULL;
