import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { validateODudeName } from 'src/lib/assets-utils';
import { hasSufficientPointsForAssetTransfer } from 'src/lib/user-settings';
import { TRANSFER_ASSET_POINT } from 'src/lib/config';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { asset_id, to_odude_name } = await request.json();

    // Validate input
    if (!asset_id || !to_odude_name) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (!validateODudeName(to_odude_name)) {
      return NextResponse.json({ error: 'Invalid ODude name format' }, { status: 400 });
    }

    // Check if user has sufficient points for asset transfer
    const pointCheck = await hasSufficientPointsForAssetTransfer(session.user.email);
    if (!pointCheck.sufficient) {
      return NextResponse.json({
        error: `Insufficient points. You need ${pointCheck.requiredPoints} points but only have ${pointCheck.currentPoints}.`
      }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user owns the asset
    const { data: asset, error: assetError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', asset_id)
      .eq('issuer_email', session.user.email)
      .eq('is_deleted', false)
      .single();

    if (assetError || !asset) {
      return NextResponse.json({ error: 'Asset not found or access denied' }, { status: 404 });
    }

    // Check if asset is expired
    if (asset.expiry_date && new Date(asset.expiry_date) < new Date()) {
      return NextResponse.json({ error: 'Cannot transfer expired asset' }, { status: 400 });
    }

    // Check if recipient ODude name exists and get their email
    const { data: recipient, error: recipientError } = await supabase
      .from('contact')
      .select('name, profile_email')
      .eq('name', to_odude_name.toLowerCase())
      .single();

    if (recipientError || !recipient) {
      return NextResponse.json({
        error: `ODude name "${to_odude_name}" does not exist. Please verify the recipient name.`
      }, { status: 400 });
    }

    const recipientEmail = recipient.profile_email;

    // Check if transfer already exists
    const { data: existingTransfer, error: existingError } = await supabase
      .from('asset_transfers')
      .select('id')
      .eq('asset_id', asset_id)
      .eq('to_odude_name', to_odude_name.toLowerCase())
      .eq('status', 'pending')
      .single();

    if (!existingError && existingTransfer) {
      return NextResponse.json({ error: 'Transfer already pending for this recipient' }, { status: 400 });
    }

    // Create transfer record
    const { data: transfer, error: transferError } = await supabase
      .from('asset_transfers')
      .insert({
        asset_id,
        from_odude_name: asset.issuer_odude_name,
        from_email: session.user.email,
        to_odude_name: to_odude_name.toLowerCase(),
        to_email: recipientEmail,
        status: 'pending'
      })
      .select()
      .single();

    if (transferError) {
      console.error('Transfer creation error:', transferError);
      return NextResponse.json({ error: 'Failed to create transfer' }, { status: 500 });
    }

    // Deduct points for asset transfer
    try {
      const { error: pointsError } = await supabase.rpc('update_user_points', {
        user_email: session.user.email,
        points_change: -TRANSFER_ASSET_POINT,
        transaction_type: 'TRANSFER_ASSET'
      });

      if (pointsError) {
        console.error('Error deducting points for asset transfer:', pointsError);
        // Don't fail the transfer if points deduction fails, just log it
      }
    } catch (error) {
      console.error('Error calling update_user_points for asset transfer:', error);
    }

    return NextResponse.json({
      success: true,
      transfer,
      message: `Asset sent to ${to_odude_name}. ${TRANSFER_ASSET_POINT} points deducted.`
    });

  } catch (error) {
    console.error('Asset transfer error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'sent' or 'received'
    const status = searchParams.get('status'); // filter by status

    const supabase = getSupabaseClient();

    let query = supabase
      .from('asset_transfers')
      .select(`
        *,
        assets (
          id,
          title,
          description,
          asset_type,
          image_url,
          issuer_odude_name,
          expiry_date,
          metadata
        )
      `);

    if (type === 'sent') {
      query = query.eq('from_email', session.user.email);
    } else if (type === 'received') {
      query = query.eq('to_email', session.user.email);
    } else {
      // Default to received transfers
      query = query.eq('to_email', session.user.email);
    }

    if (status) {
      query = query.eq('status', status);
    }

    query = query.order('transferred_at', { ascending: false });

    const { data: transfers, error } = await query;

    if (error) {
      console.error('Fetch transfers error:', error);
      return NextResponse.json({ error: 'Failed to fetch transfers' }, { status: 500 });
    }

    return NextResponse.json({ transfers });

  } catch (error) {
    console.error('Get transfers error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
