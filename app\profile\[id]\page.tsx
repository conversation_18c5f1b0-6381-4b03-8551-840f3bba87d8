// app/profile/[id]/page.tsx
// Server-side rendered component

import { getSupabaseClient } from "src/lib/supabase"
import { getSocialUrl as utilGetSocialUrl } from "src/lib/database-utils"
import { notFound } from "next/navigation"
import ClientProfilePage from "./ClientProfilePage"
import DisabledContactPage from "./DisabledContactPage"

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: Record<string, string> | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: Record<string, string> | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  minted: string | null;
  disabled: boolean;
}

function generateJsonLd(contact: ContactItem) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": contact?.profile || contact?.name,
    "image": contact?.image,
    "description": contact?.description || "ODude identity profile",
    "url": `https://odude.com/@${contact?.name}`,
    "email": contact?.email,
    "telephone": contact?.phone,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://name.odude.com/profile/${contact?.name}`
    },
    "sameAs": Object.entries(contact?.social || {}).map(([platform, handle]) =>
      utilGetSocialUrl(platform, handle)
    )
  };

  return JSON.stringify(schema);
}

export default async function ProfilePage({ params }: { params: Promise<{ id: string }> }) {
  // Handle both encoded and unencoded URLs, convert to lowercase
  const { id } = await params;
  const rawId = id;
  const contactName = rawId && rawId.includes('%') ? decodeURIComponent(rawId).toLowerCase() : rawId?.toLowerCase();

  if (!contactName) {
    notFound();
  }

  // Fetch contact data server-side
  let contact: ContactItem | null = null;

  try {
    console.log('Fetching contact:', contactName);
    const client = getSupabaseClient();
    const { data, error } = await client
      .from('contact')
      .select('*')
      .eq('name', contactName)
      .single();

    if (error) {
      console.error('Supabase error:', error);
      notFound();
    }

    if (data) {
      // Handle links, images, social, and crypto as JSON objects
      contact = {
        ...data,
        links: data.links ? (typeof data.links === 'string' ? JSON.parse(data.links) : data.links) : null,
        images: data.images ? (typeof data.images === 'string' ? JSON.parse(data.images) : data.images) : null,
        social: data.social ? (typeof data.social === 'string' ? JSON.parse(data.social) : data.social) : null,
        crypto: data.crypto ? (typeof data.crypto === 'string' ? JSON.parse(data.crypto) : data.crypto) : null
      };
    } else {
      notFound();
    }
  } catch (error) {
    console.error('Error fetching contact data:', error);
    notFound();
  }

  // Check if contact is disabled
  if (contact!.disabled) {
    return <DisabledContactPage contactName={contactName} />;
  }

  // Generate JSON-LD for SEO (contact is guaranteed to be non-null at this point)
  const jsonLd = generateJsonLd(contact!);

  return (
    <>
      {/* Server-side rendered JSON-LD for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: jsonLd }}
      />

      {/* Client component handles all interactive logic */}
      <ClientProfilePage contact={contact!} contactName={contactName} />
    </>
  );
}
