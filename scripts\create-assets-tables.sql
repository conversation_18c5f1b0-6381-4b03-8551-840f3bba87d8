-- ODude Assets System Database Schema - FRESH SETUP
-- This script creates all required tables for the complete ODude platform
-- Run this script on a fresh database (no migration needed)
--
-- Features included:
-- - Complete contact management system
-- - Primary name ownership with static/dynamic types
-- - Asset creation and management system
-- - Asset transfers between ODude names
-- - Asset approval/decline system
-- - Public asset showcase
-- - Asset templates and metadata
-- - Points system with transaction logging
-- - QR services system
-- - User profiles and authentication

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Drop existing tables if they exist (for fresh setup)
DROP TABLE IF EXISTS asset_transfers CASCADE;
DROP TABLE IF EXISTS assets CASCADE;
DROP TABLE IF EXISTS asset_templates CASCADE;
DROP TABLE IF EXISTS transaction_logs CASCADE;
DROP TABLE IF EXISTS primary_name_owners CASCADE;
DROP TABLE IF EXISTS contact CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;
DROP TABLE IF EXISTS settings CASCADE;

-- Create the profiles table (user authentication and basic info)
CREATE TABLE profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  avatar_url TEXT,
  points INTEGER DEFAULT 2000, -- Starting with signup bonus
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create the contact table (ODude Names)
CREATE TABLE contact (
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  name TEXT PRIMARY KEY,
  image TEXT,
  description TEXT,
  uri TEXT,
  profile TEXT,
  email TEXT,
  website TEXT,
  phone TEXT,
  tg_bot TEXT,
  notes JSONB,
  web2 TEXT,
  web3 TEXT,
  links JSONB,
  images JSONB,
  social JSONB,
  crypto JSONB,
  extra JSONB,
  profile_email TEXT,
  minted TEXT,
  disabled BOOLEAN DEFAULT FALSE
);

-- Create the primary_name_owners table (ownership management)
CREATE TABLE primary_name_owners (
  id SERIAL PRIMARY KEY,
  user_email TEXT NOT NULL,
  owner_of TEXT NOT NULL,
  ownership_type TEXT DEFAULT 'static' CHECK (ownership_type IN ('static', 'dynamic')),
  minted BOOLEAN DEFAULT FALSE,
  extra JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_email, owner_of)
);

-- Create the transaction_logs table (points tracking)
CREATE TABLE transaction_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_email TEXT NOT NULL,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('SIGNUP', 'CREATE_CONTACT', 'TRANSFER', 'BOOKMARK')),
  points INTEGER NOT NULL,
  from_user TEXT,
  to_user TEXT,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create the settings table (user settings and permissions)
CREATE TABLE settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  asset_issuer BOOLEAN DEFAULT FALSE,
  create_template BOOLEAN DEFAULT FALSE,
  create_contact BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- ASSETS SYSTEM TABLES
-- ============================================================================

-- Create the assets table
CREATE TABLE assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  image_url TEXT NOT NULL,
  template_id TEXT,
  issuer_odude_name TEXT NOT NULL,
  issuer_email TEXT NOT NULL,
  expiry_date TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  is_deleted BOOLEAN DEFAULT FALSE
);

-- Create the asset_transfers table (tracks all asset sends/receives)
CREATE TABLE asset_transfers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  from_odude_name TEXT NOT NULL,
  from_email TEXT NOT NULL,
  to_odude_name TEXT NOT NULL,
  to_email TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'declined', 'hidden')),
  transferred_at TIMESTAMPTZ DEFAULT NOW(),
  responded_at TIMESTAMPTZ,
  response_note TEXT
);

-- Create the asset_templates table (predefined templates)
CREATE TABLE asset_templates (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  asset_type TEXT NOT NULL CHECK (asset_type IN ('Badge', 'Certificate', 'Ticket', 'Coupon')),
  svg_code TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  profile_email TEXT, -- NULL for admin templates, email for owner-created templates
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraints
ALTER TABLE assets
ADD CONSTRAINT assets_template_id_fkey
FOREIGN KEY (template_id) REFERENCES asset_templates(id)
ON DELETE SET NULL;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Core table indexes
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_contact_profile_email ON contact(profile_email);
CREATE INDEX IF NOT EXISTS idx_contact_name ON contact(name);
CREATE INDEX IF NOT EXISTS idx_contact_disabled ON contact(disabled);

CREATE INDEX IF NOT EXISTS idx_primary_name_owners_user_email ON primary_name_owners(user_email);
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_owner_of ON primary_name_owners(owner_of);
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_ownership_type ON primary_name_owners(ownership_type);

CREATE INDEX IF NOT EXISTS idx_transaction_log_user_email ON transaction_logs(user_email);
CREATE INDEX IF NOT EXISTS idx_transaction_log_type ON transaction_logs(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_log_created_at ON transaction_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_settings_asset_issuer ON settings(asset_issuer);
CREATE INDEX IF NOT EXISTS idx_settings_create_template ON settings(create_template);
CREATE INDEX IF NOT EXISTS idx_settings_create_contact ON settings(create_contact);

-- Assets system indexes
CREATE INDEX IF NOT EXISTS idx_assets_issuer_email ON assets(issuer_email);
CREATE INDEX IF NOT EXISTS idx_assets_issuer_odude_name ON assets(issuer_odude_name);
CREATE INDEX IF NOT EXISTS idx_assets_type ON assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assets_created_at ON assets(created_at);
CREATE INDEX IF NOT EXISTS idx_assets_expiry_date ON assets(expiry_date);
CREATE INDEX IF NOT EXISTS idx_assets_deleted ON assets(is_deleted);

CREATE INDEX IF NOT EXISTS idx_asset_transfers_asset_id ON asset_transfers(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_odude_name ON asset_transfers(to_odude_name);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_email ON asset_transfers(to_email);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_from_email ON asset_transfers(from_email);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_status ON asset_transfers(status);
CREATE INDEX IF NOT EXISTS idx_asset_transfers_transferred_at ON asset_transfers(transferred_at);

CREATE INDEX IF NOT EXISTS idx_asset_templates_type ON asset_templates(asset_type);
CREATE INDEX IF NOT EXISTS idx_asset_templates_active ON asset_templates(is_active);

-- JSONB indexes
CREATE INDEX IF NOT EXISTS idx_contact_notes ON contact USING GIN (notes);
CREATE INDEX IF NOT EXISTS idx_contact_links ON contact USING GIN (links);
CREATE INDEX IF NOT EXISTS idx_contact_images ON contact USING GIN (images);
CREATE INDEX IF NOT EXISTS idx_contact_social ON contact USING GIN (social);
CREATE INDEX IF NOT EXISTS idx_contact_crypto ON contact USING GIN (crypto);
CREATE INDEX IF NOT EXISTS idx_contact_extra ON contact USING GIN (extra);

CREATE INDEX IF NOT EXISTS idx_assets_metadata ON assets USING GIN (metadata);
-- No longer need GIN index for template_data since we're using svg_code (TEXT)

-- ============================================================================
-- TABLE COMMENTS FOR DOCUMENTATION
-- ============================================================================

-- Core tables
COMMENT ON TABLE profiles IS 'User profiles with authentication and points system';
COMMENT ON TABLE contact IS 'ODude Names (contacts) with all profile information';
COMMENT ON TABLE primary_name_owners IS 'Primary name ownership assignments for admin access control';
COMMENT ON TABLE transaction_logs IS 'Points transaction history for all users';
COMMENT ON TABLE settings IS 'User settings and permissions for various features';

-- Assets tables
COMMENT ON TABLE assets IS 'Main assets table storing all created assets with metadata';
COMMENT ON TABLE asset_transfers IS 'Tracks asset transfers between ODude names with approval status';
COMMENT ON TABLE asset_templates IS 'Predefined templates for asset creation';

-- Column comments
COMMENT ON COLUMN profiles.points IS 'User points balance (starts with 2000 signup bonus)';
COMMENT ON COLUMN primary_name_owners.ownership_type IS 'static = full control, dynamic = view only';
COMMENT ON COLUMN assets.asset_type IS 'Type of asset: Badge, Certificate, Ticket, or Coupon';
COMMENT ON COLUMN assets.metadata IS 'JSON object containing additional asset metadata';
COMMENT ON COLUMN assets.template_id IS 'Reference to asset_templates.id if created from template';
COMMENT ON COLUMN assets.is_deleted IS 'Soft delete flag - when true, asset is deleted everywhere';
COMMENT ON COLUMN asset_transfers.status IS 'Transfer status: pending, approved, declined, or hidden';
COMMENT ON COLUMN asset_transfers.to_email IS 'Recipient email - can be null if ODude name not registered';

-- ============================================================================
-- VIEWS FOR COMPLEX QUERIES
-- ============================================================================

-- Create a view for assets with transfer statistics
CREATE OR REPLACE VIEW assets_with_stats AS
SELECT
  a.*,
  COALESCE(t.total_transfers, 0) as total_transfers,
  COALESCE(t.pending_transfers, 0) as pending_transfers,
  COALESCE(t.approved_transfers, 0) as approved_transfers,
  COALESCE(t.declined_transfers, 0) as declined_transfers
FROM assets a
LEFT JOIN (
  SELECT
    asset_id,
    COUNT(*) as total_transfers,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_transfers,
    COUNT(*) FILTER (WHERE status = 'approved') as approved_transfers,
    COUNT(*) FILTER (WHERE status = 'declined') as declined_transfers
  FROM asset_transfers
  GROUP BY asset_id
) t ON a.id = t.asset_id
WHERE a.is_deleted = FALSE;

-- ============================================================================
-- DEFAULT DATA INSERTION
-- ============================================================================

-- Insert default asset templates with SVG code
INSERT INTO asset_templates (id, name, description, asset_type, svg_code) VALUES
('badge_default', 'Default Badge', 'Simple badge template with gradient background', 'Badge',
'<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
  <defs>
    <linearGradient id="badgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="150" cy="150" r="140" fill="url(#badgeGradient)" stroke="#fff" stroke-width="4"/>
  <circle cx="150" cy="150" r="100" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  <text x="150" y="130" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="18" font-weight="bold">
    {{asset_title}}
  </text>
  <text x="150" y="155" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Inter, Arial, sans-serif" font-size="14">
    Awarded to
  </text>
  <text x="150" y="180" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="600">
    {{full_name}}
  </text>
  <text x="150" y="200" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
    {{issued_date}}
  </text>
</svg>'),

('certificate_default', 'Default Certificate', 'Professional certificate template with decorative borders', 'Certificate',
'<svg xmlns="http://www.w3.org/2000/svg" width="500" height="350" viewBox="0 0 500 350">
  <rect width="500" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="3"/>
  <rect x="20" y="20" width="460" height="310" fill="white" stroke="#6c757d" stroke-width="1"/>
  <rect x="30" y="30" width="440" height="40" fill="#495057"/>
  <text x="250" y="55" text-anchor="middle" fill="white" font-family="Georgia, serif" font-size="24" font-weight="bold">
    CERTIFICATE OF {{asset_title}}
  </text>
  <text x="250" y="120" text-anchor="middle" fill="#495057" font-family="Georgia, serif" font-size="16">
    This is to certify that
  </text>
  <text x="250" y="160" text-anchor="middle" fill="#212529" font-family="Georgia, serif" font-size="28" font-weight="bold">
    {{full_name}}
  </text>
  <text x="250" y="200" text-anchor="middle" fill="#495057" font-family="Georgia, serif" font-size="14">
    {{asset_description}}
  </text>
  <text x="100" y="280" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="12">
    Date: {{issued_date}}
  </text>
  <text x="400" y="280" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="12">
    Issued by: {{profile_name}}
  </text>
  <line x1="200" y1="290" x2="300" y2="290" stroke="#6c757d" stroke-width="1"/>
  <text x="250" y="305" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="10">
    Authorized Signature
  </text>
</svg>'),

('ticket_default', 'Default Ticket', 'Event ticket template with perforated edge design', 'Ticket',
'<svg xmlns="http://www.w3.org/2000/svg" width="400" height="180" viewBox="0 0 400 180">
  <rect width="300" height="180" fill="#ff6b6b" rx="10"/>
  <rect x="300" y="0" width="100" height="180" fill="#ff5252" rx="0 10 10 0"/>
  <line x1="300" y1="0" x2="300" y2="180" stroke="white" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="150" y="40" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="20" font-weight="bold">
    {{asset_title}}
  </text>
  <text x="150" y="70" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Inter, Arial, sans-serif" font-size="14">
    {{asset_description}}
  </text>
  <text x="150" y="100" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="12">
    Ticket Holder: {{full_name}}
  </text>
  <text x="150" y="125" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
    Event Date: {{issued_date}}
  </text>
  <text x="150" y="145" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
    Valid until: {{expire_date}}
  </text>
  <text x="350" y="90" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="8" transform="rotate(90 350 90)">
    ADMIT ONE
  </text>
</svg>'),

('coupon_default', 'Default Coupon', 'Discount coupon template with wavy borders', 'Coupon',
'<svg xmlns="http://www.w3.org/2000/svg" width="450" height="200" viewBox="0 0 450 200">
  <defs>
    <pattern id="wavyBorder" patternUnits="userSpaceOnUse" width="20" height="20">
      <path d="M0,10 Q5,0 10,10 Q15,20 20,10" stroke="#e74c3c" stroke-width="2" fill="none"/>
    </pattern>
  </defs>
  <rect width="450" height="200" fill="#fff5f5" stroke="url(#wavyBorder)" stroke-width="3"/>
  <rect x="20" y="20" width="410" height="160" fill="white" stroke="#e74c3c" stroke-width="1" stroke-dasharray="5,5"/>
  <text x="225" y="60" text-anchor="middle" fill="#e74c3c" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="bold">
    {{asset_title}}
  </text>
  <text x="225" y="90" text-anchor="middle" fill="#c0392b" font-family="Inter, Arial, sans-serif" font-size="16">
    {{asset_description}}
  </text>
  <text x="225" y="120" text-anchor="middle" fill="#7f8c8d" font-family="Inter, Arial, sans-serif" font-size="12">
    Valid for: {{full_name}}
  </text>
  <text x="100" y="160" text-anchor="middle" fill="#95a5a6" font-family="Inter, Arial, sans-serif" font-size="10">
    Issued: {{issued_date}}
  </text>
  <text x="350" y="160" text-anchor="middle" fill="#95a5a6" font-family="Inter, Arial, sans-serif" font-size="10">
    Expires: {{expire_date}}
  </text>
  <circle cx="50" cy="100" r="15" fill="#e74c3c"/>
  <text x="50" y="105" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="12" font-weight="bold">%</text>
</svg>')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- SETUP COMPLETE
-- ============================================================================

SELECT 'ODude Platform - Fresh Database Setup Complete!' as message,
       'All tables, indexes, views, and default data have been created.' as details;

-- Verification queries
SELECT 'Verification: Table counts' as info;
SELECT COUNT(*) as total_profiles FROM profiles;
SELECT COUNT(*) as total_contacts FROM contact;
SELECT COUNT(*) as total_owners FROM primary_name_owners;
SELECT COUNT(*) as total_assets FROM assets;
SELECT COUNT(*) as total_transfers FROM asset_transfers;
SELECT COUNT(*) as total_templates FROM asset_templates;
