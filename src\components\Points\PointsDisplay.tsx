'use client';

import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useSession } from 'next-auth/react';
import { Badge, Group, Text, Tooltip, ActionIcon, Button } from '@mantine/core';
import { IconCoins, IconRefresh } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';
import { getUserPoints } from '../../lib/points';
import { pointsEventManager } from '../../lib/pointsEvents';

interface PointsDisplayProps {
  variant?: 'badge' | 'text' | 'full' | 'button';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showRefresh?: boolean;
  clickable?: boolean;
}

export interface PointsDisplayRef {
  refreshPoints: () => void;
}

export const PointsDisplay = forwardRef<PointsDisplayRef, PointsDisplayProps>(({
  variant = 'badge',
  size = 'sm',
  showIcon = true,
  showRefresh = false,
  clickable = true
}, ref) => {
  const { data: session } = useSession();
  const router = useRouter();
  const [points, setPoints] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  const fetchPoints = async () => {
    if (!session?.user?.email) return;
    
    setLoading(true);
    try {
      const userPoints = await getUserPoints(session.user.email);
      setPoints(userPoints || 0);
    } catch (error) {
      console.error('Error fetching user points:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPoints();
  }, [session?.user?.email]);

  useEffect(() => {
    // Subscribe to points events for real-time updates
    const unsubscribe = pointsEventManager.subscribe(fetchPoints);
    return unsubscribe;
  }, []);

  useImperativeHandle(ref, () => ({
    refreshPoints: fetchPoints
  }));

  if (!session?.user?.email) {
    return null;
  }

  const formatPoints = (points: number) => {
    return points.toLocaleString();
  };

  const handleClick = () => {
    if (clickable) {
      router.push('/points');
    }
  };

  const pointsContent = (
    <Group gap="xs" align="center">
      {showIcon && <IconCoins size={16} />}
      <Text size={size} fw={500}>
        {loading ? '...' : formatPoints(points)}
      </Text>
      {showRefresh && (
        <ActionIcon
          size="xs"
          variant="subtle"
          onClick={fetchPoints}
          loading={loading}
        >
          <IconRefresh size={12} />
        </ActionIcon>
      )}
    </Group>
  );

  if (variant === 'badge') {
    return (
      <Tooltip label={clickable ? "Click to view transaction history" : "Your current point balance"}>
        <Badge
          variant="light"
          color="blue"
          size={size}
          leftSection={showIcon ? <IconCoins size={12} /> : undefined}
          style={{ cursor: clickable ? 'pointer' : 'default' }}
          onClick={handleClick}
        >
          {loading ? '...' : formatPoints(points)}
        </Badge>
      </Tooltip>
    );
  }

  if (variant === 'text') {
    return (
      <Tooltip label={clickable ? "Click to view transaction history" : "Your current point balance"}>
        <div style={{ cursor: clickable ? 'pointer' : 'default' }} onClick={handleClick}>
          {pointsContent}
        </div>
      </Tooltip>
    );
  }

  if (variant === 'full') {
    return (
      <Group gap="xs" align="center">
        <Text size="sm" c="dimmed">Points:</Text>
        <Tooltip label={clickable ? "Click to view transaction history" : "Your current point balance"}>
          <div style={{ cursor: clickable ? 'pointer' : 'default' }} onClick={handleClick}>
            {pointsContent}
          </div>
        </Tooltip>
      </Group>
    );
  }

  if (variant === 'button') {
    return (
      <Button
        variant="light"
        color="green"
        size={size}
        leftSection={showIcon ? <IconCoins size={16} /> : undefined}
        rightSection={showRefresh ? (
          <ActionIcon
            size="xs"
            variant="subtle"
            onClick={(e) => {
              e.stopPropagation();
              fetchPoints();
            }}
            loading={loading}
          >
            <IconRefresh size={12} />
          </ActionIcon>
        ) : undefined}
        onClick={handleClick}
        disabled={!clickable}
        style={{
          cursor: clickable ? 'pointer' : 'default',
          width: '100%',
          justifyContent: 'space-between'
        }}
      >
        <Group gap="xs" align="center">
          <Text size={size} fw={500}>
            {loading ? '...' : formatPoints(points)} Points
          </Text>
        </Group>
      </Button>
    );
  }

  return pointsContent;
});

PointsDisplay.displayName = 'PointsDisplay';

export default PointsDisplay;
