import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { getUserSettings } from 'src/lib/user-settings';

/**
 * Get primary names owned by a user with their ownership types
 */
async function getPrimaryNamesOwnedByUser(userEmail: string): Promise<{name: string, type: 'static' | 'dynamic'}[]> {
  const supabase = getSupabaseClient();

  const { data: owners, error } = await supabase
    .from('primary_name_owners')
    .select('owner_of, ownership_type')
    .eq('user_email', userEmail);

  if (error) {
    console.error('Error fetching primary name owners:', error);
    return [];
  }

  return owners?.map(owner => ({
    name: owner.owner_of,
    type: owner.ownership_type || 'static'
  })) || [];
}

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'No authenticated session' }, { status: 401 });
    }

    const userEmail = session.user.email;

    // Check if user has settings record (determines owner access)
    const userSettings = await getUserSettings(userEmail);
    // Owner if settings record exists AND at least one permission is enabled
    const isOwner = userSettings !== null && (
      userSettings.asset_issuer ||
      userSettings.create_template ||
      userSettings.create_contact
    );

    if (!isOwner) {
      return NextResponse.json({ error: 'Unauthorized - no owner permissions enabled' }, { status: 403 });
    }

    // Still get primary names for namespace filtering (backward compatibility)
    const ownedPrimaryNames = await getPrimaryNamesOwnedByUser(userEmail);

    const supabase = getSupabaseClient();

    let profilesCount = 0;
    let contactsCount = 0;
    let bookmarksCount = 0;

    // Owner sees only their namespace stats
    // Get contacts in their namespace
    const namespaceConditions = ownedPrimaryNames.map(primaryName =>
      `name.like.%@${primaryName}`
    ).join(',');

    if (namespaceConditions) {
      const { count: ownerContactsCount, error: contactsError } = await supabase
        .from('contact')
        .select('*', { count: 'exact', head: true })
        .or(namespaceConditions);

      if (contactsError) {
        console.error('Error fetching owner contacts count:', contactsError);
        return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
      }

      contactsCount = ownerContactsCount || 0;

      // Get profiles that have contacts in their namespace
      const { data: contactsData, error: contactsDataError } = await supabase
        .from('contact')
        .select('profile_email')
        .or(namespaceConditions);

      if (contactsDataError) {
        console.error('Error fetching contacts data for profiles count:', contactsDataError);
        return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
      }

      // Count unique profile emails
      const uniqueProfileEmails = new Set(contactsData?.map(c => c.profile_email).filter(Boolean));
      profilesCount = uniqueProfileEmails.size;

      // Get bookmarks for contacts in their namespace
      const contactNames = (await supabase
        .from('contact')
        .select('name')
        .or(namespaceConditions)).data?.map(c => c.name) || [];

      if (contactNames.length > 0) {
        const { count: ownerBookmarksCount, error: bookmarksError } = await supabase
          .from('bookmark')
          .select('*', { count: 'exact', head: true })
          .in('contact_name', contactNames);

        if (bookmarksError) {
          console.error('Error fetching owner bookmarks count:', bookmarksError);
          return NextResponse.json({ error: 'Failed to fetch bookmarks count' }, { status: 500 });
        }

        bookmarksCount = ownerBookmarksCount || 0;
      }
    }

    return NextResponse.json({
      profiles: profilesCount,
      contacts: contactsCount,
      bookmarks: bookmarksCount,
    });

  } catch (error) {
    console.error('Owner stats API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
