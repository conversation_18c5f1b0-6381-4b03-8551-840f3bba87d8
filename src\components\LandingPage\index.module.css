/* Landing Page Animations */
.heroSection {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gradientTitle {
  background: linear-gradient(45deg, var(--mantine-color-blue-6), var(--mantine-color-cyan-5));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(34, 139, 230, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(34, 139, 230, 0.6));
  }
}

.searchBox {
  position: relative;
  transition: all 0.3s ease;
}

.searchBox:hover {
  transform: translateY(-2px);
}

.searchBox input {
  font-size: 1.1rem !important;
  padding: 1rem 1.5rem !important;
  border-radius: 50px !important;
  border: 2px solid var(--mantine-color-gray-3) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.searchBox input:focus {
  border-color: var(--mantine-color-blue-5) !important;
  box-shadow: 0 0 0 3px var(--mantine-color-blue-1), 0 8px 24px rgba(34, 139, 230, 0.2) !important;
  transform: translateY(-1px) !important;
}

.featureCard {
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--mantine-color-gray-3);
  background: var(--mantine-color-body);
}

.featureCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--mantine-color-blue-3);
}

.featureIcon {
  transition: all 0.3s ease;
}

.featureCard:hover .featureIcon {
  transform: scale(1.1) rotate(5deg);
}

.ctaButton {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.ctaButton:hover::before {
  left: 100%;
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(34, 139, 230, 0.3);
}

.incentiveCard {
  animation: pulse 2s ease-in-out infinite;
  background: linear-gradient(135deg, var(--mantine-color-blue-0), var(--mantine-color-cyan-0));
  border: 1px solid var(--mantine-color-blue-2);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.floatingElements {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.floatingElement {
  position: absolute;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.floatingElement:nth-child(1) {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floatingElement:nth-child(2) {
  top: 20%;
  right: 10%;
  animation-delay: 2s;
}

.floatingElement:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Dark theme adjustments */
[data-mantine-color-scheme="dark"] .featureCard {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4);
}

[data-mantine-color-scheme="dark"] .featureCard:hover {
  border-color: var(--mantine-color-blue-4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

[data-mantine-color-scheme="dark"] .incentiveCard {
  background: linear-gradient(135deg, var(--mantine-color-dark-5), var(--mantine-color-dark-4));
  border-color: var(--mantine-color-blue-4);
}

[data-mantine-color-scheme="dark"] .searchBox input {
  background: var(--mantine-color-dark-6);
  border-color: var(--mantine-color-dark-4) !important;
  color: var(--mantine-color-dark-0);
}

[data-mantine-color-scheme="dark"] .searchBox input:focus {
  border-color: var(--mantine-color-blue-4) !important;
  box-shadow: 0 0 0 3px var(--mantine-color-blue-9), 0 8px 24px rgba(34, 139, 230, 0.3) !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .gradientTitle {
    font-size: 2.5rem !important;
  }
  
  .heroSection {
    min-height: 70vh;
    padding: 2rem 1rem;
  }
  
  .featureCard:hover {
    transform: translateY(-4px);
  }
}
