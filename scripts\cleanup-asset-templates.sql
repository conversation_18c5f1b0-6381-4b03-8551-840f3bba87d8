-- Migration script to clean up asset_templates table
-- Remove unused columns if they exist and ensure proper foreign key handling

-- First, check if unused columns exist and drop them if they do
DO $$ 
BEGIN
    -- Drop template_data column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'asset_templates' 
        AND column_name = 'template_data'
    ) THEN
        ALTER TABLE asset_templates DROP COLUMN template_data;
        RAISE NOTICE 'Dropped template_data column from asset_templates';
    END IF;

    -- Drop preview_url column if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'asset_templates' 
        AND column_name = 'preview_url'
    ) THEN
        ALTER TABLE asset_templates DROP COLUMN preview_url;
        RAISE NOTICE 'Dropped preview_url column from asset_templates';
    END IF;
END $$;

-- Ensure the foreign key constraint allows NULL values and cascading behavior
-- First drop the existing constraint if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'assets_template_id_fkey' 
        AND table_name = 'assets'
    ) THEN
        ALTER TABLE assets DROP CONSTRAINT assets_template_id_fkey;
        RAISE NOTICE 'Dropped existing foreign key constraint assets_template_id_fkey';
    END IF;
END $$;

-- Add the foreign key constraint with proper cascading behavior
-- This allows template_id to be set to NULL when a template is deleted
ALTER TABLE assets 
ADD CONSTRAINT assets_template_id_fkey 
FOREIGN KEY (template_id) REFERENCES asset_templates(id) 
ON DELETE SET NULL;

-- Add comment to explain the behavior
COMMENT ON CONSTRAINT assets_template_id_fkey ON assets IS 'Foreign key to asset_templates with ON DELETE SET NULL to handle template deletion gracefully';

-- Ensure proper indexes exist
CREATE INDEX IF NOT EXISTS idx_asset_templates_profile_email ON asset_templates(profile_email);
CREATE INDEX IF NOT EXISTS idx_asset_templates_type ON asset_templates(asset_type);
CREATE INDEX IF NOT EXISTS idx_asset_templates_active ON asset_templates(is_active);

-- Verify the final schema
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'asset_templates' 
ORDER BY ordinal_position;
