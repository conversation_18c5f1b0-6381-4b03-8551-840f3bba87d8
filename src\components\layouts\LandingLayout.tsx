'use client'

import { AppShell, Group, Container, Box } from '@mantine/core'
import React from 'react'
import { Footer } from '../Footer'
import { Logo } from '../Logo/Logo'
import { ThemeToggle } from '../Buttons/ThemeToggleButton'
import { useDeviceDetection } from "src/hooks/useDeviceDetection"
import { AdSenseBanner } from '../AdSense/AdSenseBanner'
import Link from 'next/link'

export default function LandingLayout({ children }: { children: React.ReactNode }) {
  const { isDesktop } = useDeviceDetection();

  return (
    <AppShell
      header={{ height: 80 }}
      footer={{ height: 60 }}
      aside={isDesktop ? { width: 300, breakpoint: 'md', collapsed: { desktop: false, mobile: true } } : undefined}
      padding={0}
    >
      <AppShell.Header>
        <Container size="xl" h="100%">
          <Group h="100%" justify="space-between" px="md">
            <Link href="/" style={{ textDecoration: 'none' }}>
              <Logo />
            </Link>
            <Group>
              <ThemeToggle />
            </Group>
          </Group>
        </Container>
      </AppShell.Header>

      <AppShell.Main>
        {children}
      </AppShell.Main>

      {isDesktop && (
        <AppShell.Aside p="md">
          <AdSenseBanner slot="9822011476" responsive={true} />
        </AppShell.Aside>
      )}

      <AppShell.Footer>
        <Container size="xl" h="100%">
          <Box px="md" py="sm">
            <Footer />
          </Box>
        </Container>
      </AppShell.Footer>
    </AppShell>
  )
}
