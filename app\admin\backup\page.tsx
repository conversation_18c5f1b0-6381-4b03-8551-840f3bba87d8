'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  Paper,
  Group,
  ThemeIcon,
  Stack,
  Button,
  Card,
  Badge,
  Progress,
  Divider,
  Grid,
  ActionIcon,
  Timeline,
  Code,
  Tabs,
  List,
  Anchor
} from '@mantine/core';
import {
  IconAlertCircle,
  IconDatabase,
  IconTrash,
  IconRefresh,
  IconDownload,
  IconClock,
  IconCheck,
  IconX,
  IconArrowLeft,
  IconServer,
  IconCloudUpload,
  IconShield,
  IconHistory,
  IconSettings,
  IconInfoCircle
} from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { checkAdminAccess, AdminAccessInfo } from 'src/lib/adminClient';
import { fetchBackupStatus } from 'src/lib/admin';
import { useRouter } from 'next/navigation';

interface BackupStatus {
  lastBackupTime?: string;
  message?: string;
  hasBackup: boolean;
}

interface BackupResult {
  success: boolean;
  message: string;
  files: string[];
  timestamp: string;
  lastBackupTime?: string;
}

const BACKUP_TABLES = [
  { name: 'contact', description: 'User contact information and ODude names' },
  { name: 'bookmark', description: 'User bookmarks and saved items' },
  { name: 'profiles', description: 'User profiles and settings' },
  { name: 'settings', description: 'Application settings and configurations' },
  { name: 'user_points', description: 'User point balances and rewards' },
  { name: 'transaction_logs', description: 'Point transaction history' },
  { name: 'primary_name_owners', description: 'Primary name ownership assignments' },
  { name: 'secondary_name_owners', description: 'Secondary name ownership assignments' },
  { name: 'assets', description: 'Digital assets (badges, certificates, tickets, coupons)' },
  { name: 'asset_transfers', description: 'Asset transfer tracking with approval status' },
  { name: 'asset_templates', description: 'Predefined templates for asset creation' }
];

export default function AdminBackupPage() {
  const router = useRouter();
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [backupStatus, setBackupStatus] = useState<BackupStatus | null>(null);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [lastBackupResult, setLastBackupResult] = useState<BackupResult | null>(null);

  useEffect(() => {
    checkAccess();
    fetchStatus();
  }, []);

  const checkAccess = async () => {
    try {
      const access = await checkAdminAccess();
      setAdminAccess(access);
      
      if (!access.isAuthorized || !access.isSuperAdmin) {
        router.push('/admin');
        return;
      }
    } catch (error) {
      console.error('Error checking admin access:', error);
      router.push('/admin');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatus = async () => {
    try {
      const status = await fetchBackupStatus();
      setBackupStatus(status);
    } catch (error) {
      console.error('Error fetching backup status:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to fetch backup status',
        color: 'red',
      });
    }
  };

  const handleBackupNow = async () => {
    setBackupInProgress(true);
    setBackupProgress(0);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setBackupProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 500);

      const response = await fetch('/api/backup', {
        method: 'POST',
      });

      const result: BackupResult = await response.json();
      
      clearInterval(progressInterval);
      setBackupProgress(100);
      setLastBackupResult(result);

      if (result.success) {
        notifications.show({
          title: 'Backup Successful',
          message: result.message,
          color: 'green',
        });
        
        // Refresh backup status
        await fetchStatus();
      } else {
        notifications.show({
          title: 'Backup Failed',
          message: result.message,
          color: 'red',
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to perform backup',
        color: 'red',
      });
    } finally {
      setBackupInProgress(false);
      setTimeout(() => setBackupProgress(0), 2000);
    }
  };

  const handleDeleteAllBackups = () => {
    modals.openConfirmModal({
      title: 'Delete All Backup Files',
      centered: true,
      children: (
        <Stack gap="md">
          <Text size="sm">
            Are you sure you want to delete all backup files from the FTP server?
          </Text>
          <Alert icon={<IconAlertCircle size={16} />} color="red" variant="light">
            <Text fw={500}>⚠️ Warning: This action cannot be undone</Text>
            <Text size="sm">
              This will permanently remove all backup files from the remote server.
              Make sure you have local copies if needed.
            </Text>
          </Alert>
        </Stack>
      ),
      labels: { confirm: 'Delete All Backups', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const response = await fetch('/api/admin/delete-backups', {
            method: 'DELETE',
          });

          const result = await response.json();

          if (result.success) {
            notifications.show({
              title: 'Success',
              message: result.message,
              color: 'green',
            });

            // Refresh backup status
            await fetchStatus();
          } else {
            notifications.show({
              title: 'Error',
              message: result.message || 'Failed to delete backup files',
              color: 'red',
            });
          }
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: 'Failed to delete backup files',
            color: 'red',
          });
        }
      },
    });
  };

  const formatBackupTime = (timeString?: string) => {
    if (!timeString) return 'Never';
    const date = new Date(timeString);
    return date.toLocaleString();
  };

  const getTimeSinceBackup = (timeString?: string) => {
    if (!timeString) return 'No backup found';
    const backupTime = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - backupTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Less than an hour ago';
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
          <LoadingOverlay visible />
        </Paper>
      </AdminLayout>
    );
  }

  if (!adminAccess?.isAuthorized || !adminAccess?.isSuperAdmin) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} color="red" title="Access Denied">
          You don't have permission to access the backup management page.
        </Alert>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ActionIcon
              variant="light"
              onClick={() => router.push('/admin')}
              title="Back to Admin Dashboard"
            >
              <IconArrowLeft size={16} />
            </ActionIcon>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconDatabase size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Database Backup Management</Title>
              <Text c="dimmed">
                Comprehensive backup controls for Supabase database
              </Text>
            </div>
          </Group>
          <Button
            leftSection={<IconRefresh size={16} />}
            onClick={fetchStatus}
            variant="light"
          >
            Refresh Status
          </Button>
        </Group>

        {/* Current Status */}
        <Grid>
          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <Text fw={500}>Backup Status</Text>
                <Badge 
                  color={backupStatus?.hasBackup ? 'green' : 'orange'} 
                  variant="light"
                >
                  {backupStatus?.hasBackup ? 'Active' : 'No Backups'}
                </Badge>
              </Group>
              
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text size="sm" c="dimmed">Last Backup:</Text>
                  <Text size="sm" fw={500}>
                    {formatBackupTime(backupStatus?.lastBackupTime)}
                  </Text>
                </Group>
                
                {backupStatus?.lastBackupTime && (
                  <Group justify="space-between">
                    <Text size="sm" c="dimmed">Time Since:</Text>
                    <Text size="sm">
                      {getTimeSinceBackup(backupStatus.lastBackupTime)}
                    </Text>
                  </Group>
                )}
                
                {backupStatus?.message && (
                  <Text size="xs" c="dimmed">
                    {backupStatus.message}
                  </Text>
                )}
              </Stack>
            </Card>
          </Grid.Col>

          <Grid.Col span={{ base: 12, md: 6 }}>
            <Card withBorder>
              <Text fw={500} mb="md">Quick Actions</Text>
              <Stack gap="sm">
                <Button
                  leftSection={<IconDownload size={16} />}
                  onClick={handleBackupNow}
                  loading={backupInProgress}
                  disabled={backupInProgress}
                  fullWidth
                >
                  {backupInProgress ? 'Creating Backup...' : 'Backup Now'}
                </Button>
                
                {backupStatus?.hasBackup && (
                  <Button
                    leftSection={<IconTrash size={16} />}
                    onClick={handleDeleteAllBackups}
                    color="red"
                    variant="light"
                    fullWidth
                  >
                    Delete All Backups
                  </Button>
                )}
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Backup Progress */}
        {backupInProgress && (
          <Card withBorder>
            <Text fw={500} mb="md">Backup Progress</Text>
            <Progress value={backupProgress} animated />
            <Text size="sm" c="dimmed" mt="xs">
              {backupProgress < 100 ? 'Processing tables...' : 'Backup completed!'}
            </Text>
          </Card>
        )}

        {/* Last Backup Result */}
        {lastBackupResult && (
          <Card withBorder>
            <Group justify="space-between" mb="md">
              <Text fw={500}>Last Backup Result</Text>
              <Badge color={lastBackupResult.success ? 'green' : 'red'} variant="light">
                {lastBackupResult.success ? 'Success' : 'Failed'}
              </Badge>
            </Group>
            
            <Stack gap="sm">
              <Text size="sm">{lastBackupResult.message}</Text>
              
              {lastBackupResult.files.length > 0 && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Files Created:</Text>
                  <List size="sm">
                    {lastBackupResult.files.map((file, index) => (
                      <List.Item key={index}>{file}</List.Item>
                    ))}
                  </List>
                </div>
              )}
              
              <Text size="xs" c="dimmed">
                Completed: {new Date(lastBackupResult.timestamp).toLocaleString()}
              </Text>
            </Stack>
          </Card>
        )}

        {/* Backup Information Tabs */}
        <Tabs defaultValue="tables" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="tables" leftSection={<IconDatabase size={16} />}>
              Tables
            </Tabs.Tab>
            <Tabs.Tab value="config" leftSection={<IconSettings size={16} />}>
              Configuration
            </Tabs.Tab>
            <Tabs.Tab value="security" leftSection={<IconShield size={16} />}>
              Security
            </Tabs.Tab>
            <Tabs.Tab value="automation" leftSection={<IconClock size={16} />}>
              Automation
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="tables" pt="md">
            <Card withBorder>
              <Text fw={500} mb="md">Backup Tables</Text>
              <Text size="sm" c="dimmed" mb="md">
                The following tables are included in each backup:
              </Text>

              <Stack gap="sm">
                {BACKUP_TABLES.map((table, index) => (
                  <Paper key={index} withBorder p="sm" bg="gray.0">
                    <Group justify="space-between">
                      <div>
                        <Text fw={500} size="sm">{table.name}</Text>
                        <Text size="xs" c="dimmed">{table.description}</Text>
                      </div>
                      <Badge color="blue" variant="light" size="sm">
                        CSV
                      </Badge>
                    </Group>
                  </Paper>
                ))}
              </Stack>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="config" pt="md">
            <Card withBorder>
              <Text fw={500} mb="md">Backup Configuration</Text>

              <Stack gap="md">
                <Paper withBorder p="sm" bg="blue.0">
                  <Group>
                    <ThemeIcon color="blue" variant="light">
                      <IconServer size={16} />
                    </ThemeIcon>
                    <div>
                      <Text fw={500} size="sm">FTP Server</Text>
                      <Text size="xs" c="dimmed">home.odude.com</Text>
                    </div>
                  </Group>
                </Paper>

                <Paper withBorder p="sm" bg="green.0">
                  <Group>
                    <ThemeIcon color="green" variant="light">
                      <IconCloudUpload size={16} />
                    </ThemeIcon>
                    <div>
                      <Text fw={500} size="sm">Backup Location</Text>
                      <Text size="xs" c="dimmed">/public_html/supabase_backup/</Text>
                    </div>
                  </Group>
                </Paper>

                <Paper withBorder p="sm" bg="orange.0">
                  <Group>
                    <ThemeIcon color="orange" variant="light">
                      <IconClock size={16} />
                    </ThemeIcon>
                    <div>
                      <Text fw={500} size="sm">Backup Interval</Text>
                      <Text size="xs" c="dimmed">24 hours minimum between backups</Text>
                    </div>
                  </Group>
                </Paper>
              </Stack>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="security" pt="md">
            <Card withBorder>
              <Text fw={500} mb="md">Security Features</Text>

              <Stack gap="md">
                <Alert icon={<IconShield size={16} />} color="green" variant="light">
                  <Text fw={500} size="sm">Secure Authentication</Text>
                  <Text size="xs">
                    Only super admin users can access backup functionality.
                    All API calls are authenticated and authorized.
                  </Text>
                </Alert>

                <Alert icon={<IconDatabase size={16} />} color="blue" variant="light">
                  <Text fw={500} size="sm">Service Role Access</Text>
                  <Text size="xs">
                    Uses Supabase service role key for full database access.
                    Ensures complete data export without permission restrictions.
                  </Text>
                </Alert>

                <Alert icon={<IconCloudUpload size={16} />} color="purple" variant="light">
                  <Text fw={500} size="sm">Encrypted Transfer</Text>
                  <Text size="xs">
                    Backup files are transferred securely to remote FTP server.
                    Connection credentials are stored as environment variables.
                  </Text>
                </Alert>
              </Stack>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="automation" pt="md">
            <Card withBorder>
              <Text fw={500} mb="md">Automation Options</Text>

              <Stack gap="md">
                <div>
                  <Text fw={500} size="sm" mb="xs">Cron Job Setup</Text>
                  <Text size="sm" c="dimmed" mb="sm">
                    Set up automated daily backups using cron:
                  </Text>
                  <Code block>
                    {`# Add to crontab (backup daily at 2 AM)
0 2 * * * curl -X POST https://yourdomain.com/api/backup`}
                  </Code>
                </div>

                <div>
                  <Text fw={500} size="sm" mb="xs">Manual API Call</Text>
                  <Text size="sm" c="dimmed" mb="sm">
                    Trigger backup manually via API:
                  </Text>
                  <Code block>
                    {`curl -X POST ${typeof window !== 'undefined' ? window.location.origin : 'https://yourdomain.com'}/api/backup`}
                  </Code>
                </div>

                <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
                  <Text fw={500} size="sm">Rate Limiting</Text>
                  <Text size="xs">
                    Backups are rate-limited to once every 24 hours to prevent server overload.
                    Multiple requests within this period will be rejected with a 429 status.
                  </Text>
                </Alert>
              </Stack>
            </Card>
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </AdminLayout>
  );
}
