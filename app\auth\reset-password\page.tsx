'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Container, Paper, Title, Text, Button, PasswordInput, Alert } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCheck, IconX, IconLock, IconAlertCircle } from '@tabler/icons-react';
import Link from 'next/link';

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

function ResetPasswordContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [loading, setLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);
  const [resetSuccess, setResetSuccess] = useState(false);

  const form = useForm<ResetPasswordFormData>({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validate: {
      password: (value) => {
        if (value.length < 8) return 'Password must be at least 8 characters';
        if (!/(?=.*[a-z])/.test(value)) return 'Password must contain lowercase letter';
        if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain uppercase letter';
        if (!/(?=.*\d)/.test(value)) return 'Password must contain a number';
        if (!/(?=.*[@$!%*?&])/.test(value)) return 'Password must contain special character';
        return null;
      },
      confirmPassword: (value, values) =>
        value !== values.password ? 'Passwords do not match' : null,
    },
  });

  useEffect(() => {
    if (token) {
      verifyToken();
    } else {
      setTokenValid(false);
    }
  }, [token]);

  const verifyToken = async () => {
    try {
      const response = await fetch(`/api/auth/reset-password?token=${token}`);
      const data = await response.json();
      
      setTokenValid(response.ok);
      
      if (!response.ok) {
        notifications.show({
          title: 'Invalid Reset Link',
          message: data.error || 'The reset link is invalid or has expired.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      }
    } catch (error) {
      setTokenValid(false);
      notifications.show({
        title: 'Error',
        message: 'Failed to verify reset link. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    }
  };

  const handleResetPassword = async (values: ResetPasswordFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: values.password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResetSuccess(true);
        notifications.show({
          title: 'Password Reset Successfully',
          message: 'Your password has been updated. You can now sign in with your new password.',
          color: 'green',
          icon: <IconCheck size={16} />,
        });
      } else {
        notifications.show({
          title: 'Reset Failed',
          message: data.error || 'Failed to reset password. Please try again.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!token || tokenValid === false) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <IconX size={48} color="red" style={{ marginBottom: 16 }} />
            <Title order={2} mb="md">Invalid Reset Link</Title>
            <Text c="dimmed" mb="xl">
              The password reset link is invalid or has expired. Please request a new reset link.
            </Text>
            <Button component={Link} href="/" variant="filled">
              Go to Home
            </Button>
          </div>
        </Paper>
      </Container>
    );
  }

  if (resetSuccess) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <IconCheck size={48} color="green" style={{ marginBottom: 16 }} />
            <Title order={2} mb="md">Password Reset Successfully!</Title>
            <Text c="dimmed" mb="xl">
              Your password has been updated. You can now sign in with your new password.
            </Text>
            <Alert icon={<IconCheck size={16} />} color="green" mb="xl">
              Your account is secure and ready to use.
            </Alert>
            <Button component={Link} href="/" variant="filled">
              Sign In Now
            </Button>
          </div>
        </Paper>
      </Container>
    );
  }

  if (tokenValid === null) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <Text>Verifying reset link...</Text>
          </div>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md">
        <Title order={2} mb="md" ta="center">Reset Your Password</Title>
        <Text c="dimmed" mb="xl" ta="center">
          Enter your new password below. Make sure it's strong and secure.
        </Text>

        <form onSubmit={form.onSubmit(handleResetPassword)}>
          <PasswordInput
            label="New Password"
            placeholder="Enter your new password"
            required
            leftSection={<IconLock size={16} />}
            {...form.getInputProps('password')}
            mb="md"
          />
          <PasswordInput
            label="Confirm New Password"
            placeholder="Confirm your new password"
            required
            leftSection={<IconLock size={16} />}
            {...form.getInputProps('confirmPassword')}
            mb="xl"
          />

          <Button type="submit" fullWidth loading={loading} size="md">
            Reset Password
          </Button>
        </form>

        <Text size="sm" ta="center" mt="md">
          Remember your password?{' '}
          <Link href="/" style={{ color: '#4285f4', textDecoration: 'none' }}>
            Sign in
          </Link>
        </Text>
      </Paper>
    </Container>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md">
          <div style={{ textAlign: 'center' }}>
            <Text>Loading...</Text>
          </div>
        </Paper>
      </Container>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}
