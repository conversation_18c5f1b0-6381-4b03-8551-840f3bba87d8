'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  Title,
  Text,
  Stack,
  Paper,
  Group,
  Badge,
  Button,
  ActionIcon,
  Table,
  Alert,
  LoadingOverlay,
  Select,
  TextInput,
  Card,
  ScrollArea,
  Pagination,
  ThemeIcon,
  Modal,
  Divider
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useDebouncedValue } from '@mantine/hooks';
import {
  IconHistory,
  IconSearch,
  IconFilter,
  IconRefresh,
  IconArrowLeft,
  IconTrash,
  IconAlertCircle,
  IconCoins,
  IconPhoto,
  IconUser,
  IconCalendar,
  IconArrowsExchange
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { fetchData, deleteData } from 'src/lib/supabase';
import { useAuthRedirect } from 'src/hooks/useAuthRedirect';
import { useIsOwner } from 'src/hooks/useIsOwner';
import FullLayout from 'src/components/layouts/FullLayout';
import { useRouter } from 'next/navigation';

interface AssetTransferWithAsset {
  id: string;
  asset_id: string;
  from_odude_name: string;
  from_email: string;
  to_odude_name: string;
  to_email: string;
  status: 'pending' | 'approved' | 'declined' | 'hidden';
  transferred_at: string;
  responded_at: string | null;
  response_note: string | null;
  assets: {
    id: string;
    title: string;
    description: string | null;
    asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
    image_url: string;
    issuer_odude_name: string;
    expiry_date: string | null;
  };
}

interface TransactionLog {
  id: number;
  email: string;
  transaction_type: string;
  points_change: number;
  points_before: number;
  points_after: number;
  description: string | null;
  reference_id: string | null;
  from_email: string | null;
  to_email: string | null;
  created_at: string;
}

const ITEMS_PER_PAGE = 20;

export default function OwnerTransactionPage() {
  const { session, status, isLoading } = useAuthRedirect();
  const { isOwner, ownedPrimaryNames, loading: ownerLoading } = useIsOwner();
  const router = useRouter();
  
  const [assetTransfers, setAssetTransfers] = useState<AssetTransferWithAsset[]>([]);
  const [pointTransactions, setPointTransactions] = useState<TransactionLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  
  // Filters
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [fromFilter, setFromFilter] = useState<string>('');
  const [toFilter, setToFilter] = useState<string>('');
  const [dateFrom, setDateFrom] = useState<Date | null>(null);
  const [dateTo, setDateTo] = useState<Date | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch] = useDebouncedValue(searchQuery, 300);

  useEffect(() => {
    if (session?.user?.email && isOwner) {
      fetchTransactions();
    }
  }, [session, isOwner, currentPage, debouncedSearch, typeFilter, statusFilter, fromFilter, toFilter, dateFrom, dateTo]);

  const fetchTransactions = useCallback(async () => {
    if (!session?.user?.email || !isOwner) return;

    try {
      setLoading(true);
      
      // Fetch asset transfers where the user is the issuer (from_email)
      const { data: transfers, error: transferError } = await fetchData('asset_transfers', {
        select: `
          *,
          assets (
            id,
            title,
            description,
            asset_type,
            image_url,
            issuer_odude_name,
            expiry_date
          )
        `,
        filter: [
          { column: 'from_email', value: session.user.email }
        ]
      });

      if (!transferError && transfers) {
        let filteredTransfers = Array.isArray(transfers) ? transfers : [transfers];
        
        // Apply filters
        if (statusFilter) {
          filteredTransfers = filteredTransfers.filter(t => t.status === statusFilter);
        }
        if (fromFilter) {
          filteredTransfers = filteredTransfers.filter(t => 
            t.from_odude_name.toLowerCase().includes(fromFilter.toLowerCase())
          );
        }
        if (toFilter) {
          filteredTransfers = filteredTransfers.filter(t => 
            t.to_odude_name.toLowerCase().includes(toFilter.toLowerCase())
          );
        }
        if (dateFrom) {
          filteredTransfers = filteredTransfers.filter(t => 
            new Date(t.transferred_at) >= dateFrom
          );
        }
        if (dateTo) {
          filteredTransfers = filteredTransfers.filter(t => 
            new Date(t.transferred_at) <= dateTo
          );
        }
        if (debouncedSearch) {
          filteredTransfers = filteredTransfers.filter(t => 
            t.assets?.title?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
            t.to_odude_name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
            t.from_odude_name.toLowerCase().includes(debouncedSearch.toLowerCase())
          );
        }

        setAssetTransfers(filteredTransfers);
        setTotal(filteredTransfers.length);
        setTotalPages(Math.ceil(filteredTransfers.length / ITEMS_PER_PAGE));
      }

    } catch (error) {
      console.error('Error fetching transactions:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load transaction history',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, [session, isOwner, currentPage, debouncedSearch, typeFilter, statusFilter, fromFilter, toFilter, dateFrom, dateTo]);

  const handleRevokeTransfer = async (transferId: string, assetTitle: string) => {
    modals.openConfirmModal({
      title: 'Revoke Asset Transfer',
      children: (
        <Text size="sm">
          Are you sure you want to revoke the transfer of "{assetTitle}"? 
          This action cannot be undone and the recipient will lose access to this asset.
        </Text>
      ),
      labels: { confirm: 'Revoke', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const { error } = await deleteData('asset_transfers', { column: 'id', value: transferId });

          if (error) {
            notifications.show({
              title: 'Error',
              message: 'Failed to revoke asset transfer',
              color: 'red',
            });
            return;
          }

          notifications.show({
            title: 'Success',
            message: 'Asset transfer revoked successfully',
            color: 'green',
          });

          fetchTransactions();
        } catch (error) {
          console.error('Error revoking transfer:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to revoke asset transfer',
            color: 'red',
          });
        }
      },
    });
  };

  const clearFilters = () => {
    setTypeFilter('');
    setStatusFilter('');
    setFromFilter('');
    setToFilter('');
    setDateFrom(null);
    setDateTo(null);
    setSearchQuery('');
    setCurrentPage(1);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green';
      case 'pending': return 'orange';
      case 'declined': return 'red';
      case 'hidden': return 'gray';
      default: return 'blue';
    }
  };

  // Show loading while authentication is being checked
  if (isLoading || ownerLoading) {
    return (
      <FullLayout>
        <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
          <LoadingOverlay visible />
        </Paper>
      </FullLayout>
    );
  }

  // If not authenticated, the useAuthRedirect hook will handle the redirect
  if (!session) {
    return null;
  }

  // If not an owner, show access denied
  if (!isOwner) {
    return (
      <FullLayout>
        <Alert icon={<IconAlertCircle size={16} />} color="red" title="Access Denied">
          You don't have owner permissions to view this page.
        </Alert>
      </FullLayout>
    );
  }

  return (
    <FullLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ActionIcon
              variant="light"
              onClick={() => router.push('/owner')}
              title="Back to Owner Dashboard"
            >
              <IconArrowLeft size={16} />
            </ActionIcon>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconHistory size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Transaction History</Title>
              <Text c="dimmed">
                View and manage all transactions for your namespace: {ownedPrimaryNames.join(', ')}
              </Text>
            </div>
          </Group>
          <Button
            leftSection={<IconRefresh size={16} />}
            onClick={fetchTransactions}
            loading={loading}
          >
            Refresh
          </Button>
        </Group>

        {/* Filters */}
        <Card withBorder>
          <Stack gap="md">
            <Group justify="space-between">
              <Group gap="xs">
                <IconFilter size={16} />
                <Text fw={500}>Filters</Text>
              </Group>
              <Button variant="light" size="xs" onClick={clearFilters}>
                Clear All
              </Button>
            </Group>

            <Group grow>
              <TextInput
                placeholder="Search assets, names..."
                leftSection={<IconSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Select
                placeholder="Filter by status"
                data={[
                  { value: '', label: 'All Statuses' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'approved', label: 'Approved' },
                  { value: 'declined', label: 'Declined' },
                  { value: 'hidden', label: 'Hidden' }
                ]}
                value={statusFilter}
                onChange={(value) => setStatusFilter(value || '')}
              />
            </Group>

            <Group grow>
              <TextInput
                placeholder="Filter by sender"
                value={fromFilter}
                onChange={(e) => setFromFilter(e.target.value)}
              />
              <TextInput
                placeholder="Filter by recipient"
                value={toFilter}
                onChange={(e) => setToFilter(e.target.value)}
              />
            </Group>

            <Group grow>
              <DatePickerInput
                placeholder="From date"
                value={dateFrom}
                onChange={(date) => setDateFrom(date as Date | null)}
                clearable
              />
              <DatePickerInput
                placeholder="To date"
                value={dateTo}
                onChange={(date) => setDateTo(date as Date | null)}
                clearable
              />
            </Group>
          </Stack>
        </Card>

        {/* Transaction Results */}
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Text fw={500}>Asset Transfers ({total} total)</Text>
            {totalPages > 1 && (
              <Pagination
                value={currentPage}
                onChange={setCurrentPage}
                total={totalPages}
                size="sm"
              />
            )}
          </Group>

          {loading ? (
            <LoadingOverlay visible />
          ) : assetTransfers.length === 0 ? (
            <Alert icon={<IconAlertCircle size={16} />} color="blue">
              No asset transfers found matching your criteria.
            </Alert>
          ) : (
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Asset</Table.Th>
                    <Table.Th>Recipient</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Transferred</Table.Th>
                    <Table.Th>Responded</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {assetTransfers
                    .slice((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE)
                    .map((transfer) => (
                    <Table.Tr key={transfer.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <img
                            src={transfer.assets?.image_url}
                            alt={transfer.assets?.title}
                            width={40}
                            height={40}
                            style={{ borderRadius: '4px', objectFit: 'cover' }}
                          />
                          <div>
                            <Text fw={500} size="sm">{transfer.assets?.title}</Text>
                            <Badge color="blue" variant="light" size="xs">
                              {transfer.assets?.asset_type}
                            </Badge>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{transfer.to_odude_name}</Text>
                          <Text size="xs" c="dimmed">{transfer.to_email}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(transfer.status)} variant="light">
                          {transfer.status.charAt(0).toUpperCase() + transfer.status.slice(1)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(transfer.transferred_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {transfer.responded_at
                            ? new Date(transfer.responded_at).toLocaleDateString()
                            : '-'
                          }
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <ActionIcon
                          color="red"
                          variant="light"
                          onClick={() => handleRevokeTransfer(transfer.id, transfer.assets?.title || 'Unknown Asset')}
                          title="Revoke Transfer"
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          )}
        </Card>
      </Stack>
    </FullLayout>
  );
}
